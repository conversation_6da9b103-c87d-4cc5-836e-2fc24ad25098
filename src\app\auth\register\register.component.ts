import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { ApiService, RegisterRequest } from '../../services/api.service';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule],
  templateUrl: './register.component.html',
  styleUrl: './register.component.scss'
})
export class RegisterComponent implements OnInit {
  registerForm!: FormGroup;
  loading = false;
  error = '';
  returnUrl: string = '/';

  constructor(
    private fb: FormBuilder,
    private apiService: ApiService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    // Get return URL from route parameters or default to '/'
    this.route.queryParams.subscribe(params => {
      this.returnUrl = params['returnUrl'] || '/';
      console.log('Return URL set to:', this.returnUrl);
    });
  }

  initForm(): void {
    this.registerForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      phoneNumber: [''],
      companyName: [''],
      jobTitle: ['']
    });
  }

  onSubmit(): void {
    if (this.registerForm.invalid) {
      this.markFormGroupTouched(this.registerForm);
      return;
    }

    this.loading = true;
    this.error = '';

    // Ensure all required fields have values
    const registerData: RegisterRequest = {
      firstName: this.registerForm.value.firstName || '',
      lastName: this.registerForm.value.lastName || '',
      email: this.registerForm.value.email || '',
      password: this.registerForm.value.password || '',
      phoneNumber: this.registerForm.value.phoneNumber || '',
      companyName: this.registerForm.value.companyName || '',
      jobTitle: this.registerForm.value.jobTitle || ''
    };

    console.log('Submitting registration data:', registerData);

    this.apiService.register(registerData).subscribe({
      next: (response) => {
        console.log('Registration response:', response);
        this.loading = false;
        if (response.success) {
          console.log('Registration successful, navigating to:', this.returnUrl);
          // Add a small delay to ensure the token is stored properly
          setTimeout(() => {
            this.router.navigateByUrl(this.returnUrl);
          }, 500);
        } else {
          console.error('Registration failed with message:', response.message);
          this.error = response.message || 'Registration failed';
        }
      },
      error: (err) => {
        console.error('Registration error details:', JSON.stringify(err));
        this.loading = false;

        if (err.status === 0) {
          this.error = 'Cannot connect to the server. Please check your internet connection or try again later.';
          return;
        }

        if (err.status === 400) {
          if (err.error && err.error.errors) {
            // Handle validation errors
            const validationErrors = Object.values(err.error.errors).flat();
            this.error = validationErrors.join(', ');
          } else if (err.error && err.error.message) {
            this.error = err.error.message;
          } else if (err.error && typeof err.error === 'string') {
            this.error = err.error;
          } else {
            this.error = 'Invalid registration data. Please check your information and try again.';
          }
          return;
        }

        if (err.status === 500) {
          this.error = 'Server error. Please try again later.';
          return;
        }

        // Default error handling
        if (err.error && typeof err.error === 'object') {
          console.error('Error object:', err.error);
          this.error = err.error.message || 'An error occurred during registration';
        } else if (typeof err.error === 'string') {
          console.error('Error string:', err.error);
          this.error = err.error || 'An error occurred during registration';
        } else {
          console.error('Unknown error format:', err);
          this.error = 'An error occurred during registration';
        }
      }
    });
  }

  // Helper method to mark all form controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
