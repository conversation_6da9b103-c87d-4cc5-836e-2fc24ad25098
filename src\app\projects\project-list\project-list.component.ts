import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ApiService, Project } from '../../services/api.service';

@Component({
  selector: 'app-project-list',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './project-list.component.html',
  styleUrl: './project-list.component.scss'
})
export class ProjectListComponent implements OnInit {
  projects: Project[] = [];
  filteredProjects: Project[] = [];
  loading = false;
  error = '';
  
  // Filter options
  statusFilter: string = 'all';
  categoryFilter: string = 'all';
  searchTerm: string = '';
  
  // Status and category options for filters
  statusOptions = ['all', 'Upcoming', 'Active', 'Completed', 'Cancelled'];
  categoryOptions = ['all', 'Community', 'Education', 'Environment', 'Health', 'Technology', 'Arts', 'Other'];

  constructor(private apiService: ApiService) {}

  ngOnInit(): void {
    this.loadProjects();
  }

  loadProjects(): void {
    this.loading = true;
    this.apiService.getProjects().subscribe({
      next: (response) => {
        this.projects = response.projects;
        this.applyFilters();
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load projects. Please try again.';
        this.loading = false;
        console.error('Error loading projects:', err);
      }
    });
  }

  applyFilters(): void {
    this.filteredProjects = this.projects.filter(project => {
      // Apply status filter
      if (this.statusFilter !== 'all' && project.status !== this.statusFilter) {
        return false;
      }
      
      // Apply category filter
      if (this.categoryFilter !== 'all' && project.category !== this.categoryFilter) {
        return false;
      }
      
      // Apply search term
      if (this.searchTerm && !this.matchesSearchTerm(project)) {
        return false;
      }
      
      return true;
    });
  }

  matchesSearchTerm(project: Project): boolean {
    const term = this.searchTerm.toLowerCase();
    return (
      project.title.toLowerCase().includes(term) ||
      project.description.toLowerCase().includes(term) ||
      project.location.toLowerCase().includes(term) ||
      project.companyName.toLowerCase().includes(term)
    );
  }

  onStatusFilterChange(status: string): void {
    this.statusFilter = status;
    this.applyFilters();
  }

  onCategoryFilterChange(category: string): void {
    this.categoryFilter = category;
    this.applyFilters();
  }

  onSearchChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.searchTerm = input.value;
    this.applyFilters();
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Upcoming':
        return 'status-upcoming';
      case 'Active':
        return 'status-active';
      case 'Completed':
        return 'status-completed';
      case 'Cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  deleteProject(id: number, event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    
    if (confirm('Are you sure you want to delete this project?')) {
      this.apiService.deleteProject(id).subscribe({
        next: () => {
          this.projects = this.projects.filter(p => p.id !== id);
          this.applyFilters();
        },
        error: (err) => {
          this.error = 'Failed to delete project. Please try again.';
          console.error('Error deleting project:', err);
        }
      });
    }
  }
}
