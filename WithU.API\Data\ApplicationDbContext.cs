using Microsoft.EntityFrameworkCore;
using WithU.API.Models;

namespace WithU.API.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Project> Projects { get; set; }
        public DbSet<ProjectParticipation> ProjectParticipations { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(50);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Password).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Role).IsRequired().HasMaxLength(20);
            });

            // Configure Project entity
            modelBuilder.Entity<Project>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.Location).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Date).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Category).HasMaxLength(50);
                entity.Property(e => e.CompanyName).HasMaxLength(100);
                entity.Property(e => e.SkillsRequired).HasMaxLength(200);
                entity.Property(e => e.ContactEmail).HasMaxLength(100);
                entity.Property(e => e.ContactPhone).HasMaxLength(20);
            });

            // Configure ProjectParticipation entity
            modelBuilder.Entity<ProjectParticipation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Feedback).HasMaxLength(500);
                entity.Property(e => e.UserFullName).HasMaxLength(100);
                entity.Property(e => e.UserEmail).HasMaxLength(100);
                entity.Property(e => e.ProjectTitle).HasMaxLength(100);

                // Define relationships
                // In a real application with proper EF Core relationships:
                // entity.HasOne<User>().WithMany().HasForeignKey(e => e.UserId);
                // entity.HasOne<Project>().WithMany().HasForeignKey(e => e.ProjectId);
            });

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed Users
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    FirstName = "John",
                    LastName = "Doe",
                    Email = "<EMAIL>",
                    Password = "password123", // In a real app, this would be hashed
                    PhoneNumber = "(*************",
                    ProfileImage = "https://randomuser.me/api/portraits/men/1.jpg",
                    Bio = "Passionate about community service and making a difference.",
                    Skills = "Leadership, Communication, Project Management",
                    CompanyName = "Tech Innovations Inc.",
                    JobTitle = "Senior Developer",
                    Role = "Employee",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new User
                {
                    Id = 2,
                    FirstName = "Jane",
                    LastName = "Smith",
                    Email = "<EMAIL>",
                    Password = "password123", // In a real app, this would be hashed
                    PhoneNumber = "(*************",
                    ProfileImage = "https://randomuser.me/api/portraits/women/1.jpg",
                    Bio = "Dedicated to helping others and building stronger communities.",
                    Skills = "Teaching, Mentoring, Organization",
                    CompanyName = "Green Earth Inc.",
                    JobTitle = "Community Outreach Manager",
                    Role = "CompanyAdmin",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new User
                {
                    Id = 3,
                    FirstName = "Admin",
                    LastName = "User",
                    Email = "<EMAIL>",
                    Password = "admin123", // In a real app, this would be hashed
                    PhoneNumber = "(*************",
                    ProfileImage = "https://randomuser.me/api/portraits/men/10.jpg",
                    Bio = "System administrator for the WithU platform.",
                    Skills = "Administration, Technical Support, User Management",
                    CompanyName = "WithU",
                    JobTitle = "System Administrator",
                    Role = "SystemAdmin",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            );

            // Seed Projects
            modelBuilder.Entity<Project>().HasData(
                new Project
                {
                    Id = 1,
                    Title = "Community Garden Initiative",
                    Description = "Help create and maintain community gardens in underserved neighborhoods.",
                    Image = "https://images.unsplash.com/photo-1464226184884-fa280b87c399",
                    Participants = 2,
                    Location = "Various Locations",
                    Date = "2023-12-15",
                    Status = "Active",
                    Category = "Environment",
                    CompanyName = "Green Earth Inc.",
                    SkillsRequired = "Gardening, Basic Construction",
                    MaxParticipants = 50,
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "(*************",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Project
                {
                    Id = 2,
                    Title = "Youth Mentorship Program",
                    Description = "Connect with local youth to provide guidance, support, and positive role modeling.",
                    Image = "https://images.unsplash.com/photo-1529390079861-591de354faf5",
                    Participants = 0,
                    Location = "Community Center",
                    Date = "2023-12-20",
                    Status = "Upcoming",
                    Category = "Education",
                    CompanyName = "Future Leaders Foundation",
                    SkillsRequired = "Communication, Patience, Mentoring",
                    MaxParticipants = 30,
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "(*************",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Project
                {
                    Id = 3,
                    Title = "Food Bank Volunteers",
                    Description = "Help sort, package, and distribute food to families in need.",
                    Image = "https://images.unsplash.com/photo-*************-ea4288922497",
                    Participants = 1,
                    Location = "Downtown Food Bank",
                    Date = "2023-12-10",
                    Status = "Active",
                    Category = "Community",
                    CompanyName = "Nourish Together",
                    SkillsRequired = "Organization, Teamwork",
                    MaxParticipants = 40,
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "(*************",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Project
                {
                    Id = 4,
                    Title = "Neighborhood Cleanup",
                    Description = "Join us in cleaning up local parks and streets to create a cleaner community.",
                    Image = "https://images.unsplash.com/photo-*************-cf6ed80faba5",
                    Participants = 0,
                    Location = "City Park",
                    Date = "2023-12-05",
                    Status = "Upcoming",
                    Category = "Environment",
                    CompanyName = "Clean City Initiative",
                    SkillsRequired = "None",
                    MaxParticipants = 100,
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "(*************",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            );

            // Seed ProjectParticipations
            modelBuilder.Entity<ProjectParticipation>().HasData(
                new ProjectParticipation
                {
                    Id = 1,
                    UserId = 1,
                    ProjectId = 1,
                    Status = "Confirmed",
                    HoursLogged = 4,
                    Feedback = "Great experience! Looking forward to the next event.",
                    Rating = 5,
                    JoinedAt = DateTime.UtcNow.AddDays(-15),
                    AttendedAt = DateTime.UtcNow.AddDays(-5),
                    UserFullName = "John Doe",
                    UserEmail = "<EMAIL>",
                    ProjectTitle = "Community Garden Initiative"
                },
                new ProjectParticipation
                {
                    Id = 2,
                    UserId = 1,
                    ProjectId = 3,
                    Status = "Registered",
                    JoinedAt = DateTime.UtcNow.AddDays(-3),
                    UserFullName = "John Doe",
                    UserEmail = "<EMAIL>",
                    ProjectTitle = "Food Bank Volunteers"
                },
                new ProjectParticipation
                {
                    Id = 3,
                    UserId = 2,
                    ProjectId = 1,
                    Status = "Confirmed",
                    HoursLogged = 6,
                    Feedback = "Well organized and impactful.",
                    Rating = 4,
                    JoinedAt = DateTime.UtcNow.AddDays(-20),
                    AttendedAt = DateTime.UtcNow.AddDays(-5),
                    UserFullName = "Jane Smith",
                    UserEmail = "<EMAIL>",
                    ProjectTitle = "Community Garden Initiative"
                }
            );
        }
    }
}
