using Microsoft.EntityFrameworkCore;
using WithU.API.Data;
using WithU.API.Models;

namespace WithU.API.Repositories
{
    public class EfProjectParticipationRepository : IProjectParticipationRepository
    {
        private readonly ApplicationDbContext _context;

        public EfProjectParticipationRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<ProjectParticipation>> GetAllAsync()
        {
            return await _context.ProjectParticipations.ToListAsync();
        }

        public async Task<ProjectParticipation?> GetByIdAsync(int id)
        {
            return await _context.ProjectParticipations.FindAsync(id);
        }

        public async Task<IEnumerable<ProjectParticipation>> GetByUserIdAsync(int userId)
        {
            return await _context.ProjectParticipations
                .Where(p => p.UserId == userId)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProjectParticipation>> GetByProjectIdAsync(int projectId)
        {
            return await _context.ProjectParticipations
                .Where(p => p.ProjectId == projectId)
                .ToListAsync();
        }

        public async Task<ProjectParticipation?> GetByUserAndProjectIdAsync(int userId, int projectId)
        {
            return await _context.ProjectParticipations
                .FirstOrDefaultAsync(p => p.UserId == userId && p.ProjectId == projectId);
        }

        public async Task<ProjectParticipation> CreateAsync(ProjectParticipation participation)
        {
            // Get user and project details to populate the participation record
            var user = await _context.Users.FindAsync(participation.UserId);
            var project = await _context.Projects.FindAsync(participation.ProjectId);

            if (user != null && project != null)
            {
                participation.UserFullName = $"{user.FirstName} {user.LastName}";
                participation.UserEmail = user.Email;
                participation.ProjectTitle = project.Title;
            }

            participation.JoinedAt = DateTime.UtcNow;
            _context.ProjectParticipations.Add(participation);
            await _context.SaveChangesAsync();

            // Update the project's participant count
            if (project != null)
            {
                project.Participants = await GetParticipantCountForProjectAsync(project.Id);
                await _context.SaveChangesAsync();
            }

            return participation;
        }

        public async Task<ProjectParticipation?> UpdateAsync(int id, ProjectParticipation updatedParticipation)
        {
            var existingParticipation = await _context.ProjectParticipations.FindAsync(id);
            if (existingParticipation == null)
            {
                return null;
            }

            // Update properties
            existingParticipation.Status = updatedParticipation.Status;
            existingParticipation.HoursLogged = updatedParticipation.HoursLogged;
            existingParticipation.Feedback = updatedParticipation.Feedback;
            existingParticipation.Rating = updatedParticipation.Rating;

            // Update timestamps based on status
            if (updatedParticipation.Status == "Cancelled" && existingParticipation.CancelledAt == null)
            {
                existingParticipation.CancelledAt = DateTime.UtcNow;
            }
            else if (updatedParticipation.Status == "Attended" && existingParticipation.AttendedAt == null)
            {
                existingParticipation.AttendedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            // Update the project's participant count if status changed
            var project = await _context.Projects.FindAsync(existingParticipation.ProjectId);
            if (project != null)
            {
                project.Participants = await GetParticipantCountForProjectAsync(project.Id);
                await _context.SaveChangesAsync();
            }

            return existingParticipation;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var participation = await _context.ProjectParticipations.FindAsync(id);
            if (participation == null)
            {
                return false;
            }

            _context.ProjectParticipations.Remove(participation);
            await _context.SaveChangesAsync();

            // Update the project's participant count
            var project = await _context.Projects.FindAsync(participation.ProjectId);
            if (project != null)
            {
                project.Participants = await GetParticipantCountForProjectAsync(project.Id);
                await _context.SaveChangesAsync();
            }

            return true;
        }

        public async Task<int> GetParticipantCountForProjectAsync(int projectId)
        {
            // Count active participants (Registered or Confirmed, not Cancelled)
            return await _context.ProjectParticipations
                .CountAsync(p => 
                    p.ProjectId == projectId && 
                    (p.Status == "Registered" || p.Status == "Confirmed" || p.Status == "Attended"));
        }
    }
}
