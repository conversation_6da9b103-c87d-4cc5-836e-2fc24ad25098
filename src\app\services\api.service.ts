import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, tap, catchError, throwError } from 'rxjs';

// API URL for .NET backend server
export const API_URL = 'http://localhost:5080/api';

export interface Project {
  id: number;
  title: string;
  description: string;
  image: string;
  participants: number;
  location: string;
  date: string;
  status: string;
  category: string;
  companyName: string;
  skillsRequired: string;
  maxParticipants: number;
  contactEmail: string;
  contactPhone: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProjectRequest {
  title: string;
  description: string;
  image: string;
  location: string;
  date: string;
  status: string;
  category: string;
  companyName: string;
  skillsRequired: string;
  maxParticipants: number;
  contactEmail: string;
  contactPhone: string;
}

export interface Service {
  id: number;
  title: string;
  description: string;
  icon: string;
}

export interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  profileImage?: string;
  companyName?: string;
  jobTitle?: string;
  phoneNumber?: string;
  bio?: string;
  skills?: string;
  role: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phoneNumber?: string;
  companyName?: string;
  jobTitle?: string;
}

export interface AuthResponse {
  success: boolean;
  token?: string;
  message?: string;
  user?: User;
}

export interface ContactRequest {
  name: string;
  email: string;
  message: string;
}

export interface ContactResponse {
  success: boolean;
  message?: string;
}

export interface ProjectParticipation {
  id: number;
  userId: number;
  projectId: number;
  status: string;
  hoursLogged?: number;
  feedback?: string;
  rating?: number;
  joinedAt: string;
  cancelledAt?: string;
  attendedAt?: string;
  userFullName: string;
  userEmail: string;
  projectTitle: string;
}

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser = this.currentUserSubject.asObservable();

  private tokenSubject = new BehaviorSubject<string | null>(this.getStoredToken());
  public token = this.tokenSubject.asObservable();

  constructor(private http: HttpClient) {
    // Try to load user from storage on initialization
    this.loadUserFromStorage();
  }

  private getStoredToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  private setStoredToken(token: string | null): void {
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  private loadUserFromStorage(): void {
    const token = this.getStoredToken();
    if (token) {
      this.getCurrentUser().subscribe({
        next: (user) => {
          this.currentUserSubject.next(user);
        },
        error: () => {
          // Token is invalid or expired
          this.logout();
        }
      });
    }
  }

  private getAuthHeaders(): HttpHeaders {
    const token = this.tokenSubject.value;
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });
  }

  // Auth methods
  login(credentials: LoginRequest): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${API_URL}/auth/login`, credentials)
      .pipe(
        tap(response => {
          if (response.success && response.token && response.user) {
            this.tokenSubject.next(response.token);
            this.currentUserSubject.next(response.user);
            this.setStoredToken(response.token);
          }
        })
      );
  }

  register(userData: RegisterRequest): Observable<AuthResponse> {
    console.log('API Service - Registering user:', userData);
    return this.http.post<AuthResponse>(`${API_URL}/auth/register`, userData)
      .pipe(
        tap(response => {
          console.log('API Service - Registration response:', response);
          if (response.success && response.token && response.user) {
            console.log('API Service - Setting auth token and user');
            this.tokenSubject.next(response.token);
            this.currentUserSubject.next(response.user);
            this.setStoredToken(response.token);
          }
        }),
        catchError(error => {
          console.error('API Service - Registration error:', error);
          return throwError(() => error);
        })
      );
  }

  getCurrentUser(): Observable<User> {
    return this.http.get<User>(`${API_URL}/auth/user`, { headers: this.getAuthHeaders() })
      .pipe(
        catchError(error => {
          this.logout();
          return throwError(() => error);
        })
      );
  }

  logout(): void {
    this.tokenSubject.next(null);
    this.currentUserSubject.next(null);
    this.setStoredToken(null);
  }

  isLoggedIn(): boolean {
    return !!this.tokenSubject.value;
  }

  sendContactMessage(contactData: ContactRequest): Observable<ContactResponse> {
    console.log('API Service - Sending contact message:', contactData);
    return this.http.post<ContactResponse>(`${API_URL}/auth/contact`, contactData)
      .pipe(
        catchError(error => {
          console.error('API Service - Contact message error:', error);
          return throwError(() => error);
        })
      );
  }

  // User methods
  getUsers(): Observable<{ users: User[] }> {
    return this.http.get<{ users: User[] }>(`${API_URL}/users`, { headers: this.getAuthHeaders() });
  }

  getUserById(id: number): Observable<User> {
    return this.http.get<User>(`${API_URL}/users/${id}`, { headers: this.getAuthHeaders() });
  }

  updateUser(id: number, user: any): Observable<User> {
    console.log('API Service - Updating user:', user);
    return this.http.put<User>(`${API_URL}/users/${id}`, user, { headers: this.getAuthHeaders() })
      .pipe(
        tap(updatedUser => {
          console.log('API Service - User updated:', updatedUser);
        }),
        catchError(error => {
          console.error('API Service - Error updating user:', error);
          return throwError(() => error);
        })
      );
  }

  // Method to update the current user in the BehaviorSubject
  updateCurrentUser(user: User): void {
    console.log('API Service - Updating current user in service:', user);
    this.currentUserSubject.next(user);
  }

  getUserParticipations(userId: number): Observable<{ participations: ProjectParticipation[] }> {
    return this.http.get<{ participations: ProjectParticipation[] }>(`${API_URL}/users/${userId}/participations`, { headers: this.getAuthHeaders() });
  }

  // Project methods
  getProjects(): Observable<{ projects: Project[] }> {
    return this.http.get<{ projects: Project[] }>(`${API_URL}/projects`);
  }

  getProjectById(id: number): Observable<Project> {
    return this.http.get<Project>(`${API_URL}/projects/${id}`);
  }

  createProject(project: CreateProjectRequest): Observable<Project> {
    console.log('API Service - Creating project:', project);
    return this.http.post<Project>(`${API_URL}/projects`, project, { headers: this.getAuthHeaders() })
      .pipe(
        tap(createdProject => {
          console.log('API Service - Project created:', createdProject);
        }),
        catchError(error => {
          console.error('API Service - Error creating project:', error);
          return throwError(() => error);
        })
      );
  }

  updateProject(id: number, project: Project): Observable<Project> {
    console.log('API Service - Updating project:', project);
    return this.http.put<Project>(`${API_URL}/projects/${id}`, project, { headers: this.getAuthHeaders() })
      .pipe(
        tap(updatedProject => {
          console.log('API Service - Project updated:', updatedProject);
        }),
        catchError(error => {
          console.error('API Service - Error updating project:', error);
          return throwError(() => error);
        })
      );
  }

  deleteProject(id: number): Observable<void> {
    return this.http.delete<void>(`${API_URL}/projects/${id}`, { headers: this.getAuthHeaders() });
  }

  // Participation methods
  getProjectParticipations(projectId: number): Observable<{ participations: ProjectParticipation[] }> {
    return this.http.get<{ participations: ProjectParticipation[] }>(`${API_URL}/participations/project/${projectId}`, { headers: this.getAuthHeaders() });
  }

  getUserProjectParticipation(userId: number, projectId: number): Observable<ProjectParticipation> {
    return this.http.get<ProjectParticipation>(`${API_URL}/participations/user/${userId}/project/${projectId}`, { headers: this.getAuthHeaders() });
  }

  joinProject(userId: number, projectId: number): Observable<ProjectParticipation> {
    const participation: Partial<ProjectParticipation> = {
      userId,
      projectId,
      status: 'Registered'
    };
    return this.http.post<ProjectParticipation>(`${API_URL}/participations`, participation, { headers: this.getAuthHeaders() });
  }

  updateParticipation(id: number, participation: Partial<ProjectParticipation>): Observable<ProjectParticipation> {
    return this.http.put<ProjectParticipation>(`${API_URL}/participations/${id}`, participation, { headers: this.getAuthHeaders() });
  }

  leaveProject(participationId: number): Observable<void> {
    return this.http.delete<void>(`${API_URL}/participations/${participationId}`, { headers: this.getAuthHeaders() });
  }

  // Service methods
  getServices(): Observable<{ services: Service[] }> {
    return this.http.get<{ services: Service[] }>(`${API_URL}/services`);
  }

  getServiceById(id: number): Observable<Service> {
    return this.http.get<Service>(`${API_URL}/services/${id}`);
  }
}
