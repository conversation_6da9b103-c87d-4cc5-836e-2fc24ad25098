using System;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using WithU.API.Data;

namespace WithU.API
{
    public class Program
    {
        public static void Main(string[] args)
        {
            using (var context = new ApplicationDbContext(
                new DbContextOptionsBuilder<ApplicationDbContext>()
                    .UseSqlite("Data Source=withu.db")
                    .Options))
            {
                Console.WriteLine("Users in database:");
                Console.WriteLine("==================");
                
                var users = context.Users.ToList();
                foreach (var user in users)
                {
                    Console.WriteLine($"ID: {user.Id}");
                    Console.WriteLine($"Name: {user.FirstName} {user.LastName}");
                    Console.WriteLine($"Email: {user.Email}");
                    Console.WriteLine($"Role: {user.Role}");
                    Console.WriteLine($"Created: {user.CreatedAt}");
                    Console.WriteLine("------------------");
                }
            }
        }
    }
}
