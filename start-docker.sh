#!/bin/bash

echo "==================================================="
echo "       STARTING WITHU APPLICATION WITH DOCKER"
echo "==================================================="
echo ""

echo "[1/3] Checking prerequisites..."
if ! command -v docker &> /dev/null; then
    echo "ERROR: Docker not found. Please install Docker."
    echo "Download from: https://www.docker.com/products/docker-desktop"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "ERROR: Docker Compose not found. Please install Docker Compose."
    echo "See: https://docs.docker.com/compose/install/"
    exit 1
fi

echo "[2/3] Building and starting containers..."
docker-compose up -d --build

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to start Docker containers."
    exit 1
fi

echo "[3/3] Opening application in browser..."
sleep 5

# Try to open browser based on OS
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    open http://localhost:4200
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:4200
    elif command -v gnome-open &> /dev/null; then
        gnome-open http://localhost:4200
    else
        echo "Could not automatically open browser. Please visit http://localhost:4200"
    fi
else
    echo "Could not automatically open browser. Please visit http://localhost:4200"
fi

echo ""
echo "==================================================="
echo "       WITHU APPLICATION STARTED WITH DOCKER"
echo "==================================================="
echo ""
echo "API: http://localhost:5080"
echo "Frontend: http://localhost:4200"
echo ""
echo "Your browser should open automatically to http://localhost:4200"
echo ""
echo "To stop the application, run: docker-compose down"
echo ""
