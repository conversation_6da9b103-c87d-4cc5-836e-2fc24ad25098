import { Component, OnInit, HostListener } from '@angular/core';
import { RouterOutlet, RouterModule, Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { filter } from 'rxjs/operators';
import { ApiService, User } from './services/api.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, RouterModule, CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'WithU';
  isMobileMenuOpen = false;
  isScrolled = false;
  currentUser: User | null = null;

  // Contact form properties
  contactForm!: FormGroup;
  contactLoading = false;
  contactSuccess = '';
  contactError = '';

  constructor(private router: Router, public apiService: ApiService, private fb: FormBuilder) {
    this.initContactForm();
  }

  ngOnInit() {
    // Get current user
    this.apiService.currentUser.subscribe(user => {
      this.currentUser = user;
    });

    // Handle fragment navigation
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      // Get the fragment from the URL
      const fragment = this.router.parseUrl(this.router.url).fragment;

      // If we're on the home page and there's a fragment, scroll to it
      if (this.router.url.startsWith('/?') || this.router.url === '/') {
        if (fragment) {
          setTimeout(() => {
            const element = document.getElementById(fragment);
            if (element) {
              element.scrollIntoView({ behavior: 'smooth' });
            }
          }, 300);
        } else {
          // If no fragment on home page, scroll to top
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }
      } else {
        // For other pages, always scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }

      // Close mobile menu when navigating
      this.isMobileMenuOpen = false;
    });
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    this.isScrolled = window.scrollY > 50;
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  // No longer needed since we removed authentication
  // logout() {
  //   this.router.navigate(['/']);
  // }

  // We no longer need these methods as we're using Angular's built-in fragment navigation
  // They're kept here as empty methods in case they're referenced elsewhere
  navigateTo(route: string) {
    // This method is no longer needed
    this.isMobileMenuOpen = false;
    return false;
  }

  scrollToSection(sectionId: string) {
    // This method is no longer needed
    this.isMobileMenuOpen = false;
  }

  scrollToFooter() {
    // This method is no longer needed
    this.isMobileMenuOpen = false;
  }

  // Contact form methods
  initContactForm(): void {
    this.contactForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      message: ['', [Validators.required, Validators.minLength(10)]]
    });
  }

  onContactSubmit(): void {
    if (this.contactForm.invalid) {
      this.markFormGroupTouched(this.contactForm);
      return;
    }

    this.contactLoading = true;
    this.contactError = '';
    this.contactSuccess = '';

    const contactData = this.contactForm.value;

    // Send message to backend API
    this.apiService.sendContactMessage(contactData).subscribe({
      next: (response) => {
        this.contactLoading = false;
        if (response.success) {
          this.contactSuccess = response.message || 'Thank you for your message! We will get back to you soon.';
          this.contactForm.reset();

          // Clear success message after 5 seconds
          setTimeout(() => {
            this.contactSuccess = '';
          }, 5000);
        } else {
          this.contactError = response.message || 'Failed to send message. Please try again.';
        }
      },
      error: (err) => {
        this.contactLoading = false;
        if (err.status === 0) {
          this.contactError = 'Cannot connect to the server. Please check your internet connection.';
        } else if (err.error && err.error.message) {
          this.contactError = err.error.message;
        } else {
          this.contactError = 'Failed to send message. Please try again.';
        }
        console.error('Contact form error:', err);
      }
    });
  }

  // Helper method to mark all form controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
