# PostgreSQL Setup for WithU Application

This guide will help you set up PostgreSQL for the WithU application.

## Prerequisites

1. **Install PostgreSQL**
   - **Windows**: Download from https://www.postgresql.org/download/windows/
   - **macOS**: Use Homebrew: `brew install postgresql`
   - **Linux**: Use your package manager, e.g., `sudo apt-get install postgresql postgresql-contrib`

2. **Install pgAdmin** (Optional but recommended)
   - Download from https://www.pgadmin.org/download/

## Setup Steps

### 1. Start PostgreSQL Service

**Windows:**
- PostgreSQL should start automatically after installation
- Or use Services app to start "postgresql-x64-xx" service

**macOS:**
```bash
brew services start postgresql
```

**Linux:**
```bash
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. Create Databases

#### Option A: Using psql command line
```bash
# Connect to PostgreSQL as postgres user
psql -U postgres

# Run the setup script
\i WithU.API/setup-postgresql.sql

# Exit psql
\q
```

#### Option B: Using pgAdmin
1. Open pgAdmin
2. Connect to your PostgreSQL server
3. Right-click on "Databases" → "Create" → "Database"
4. Create databases: `withu_db` and `withu_dev_db`

#### Option C: Manual commands
```sql
-- Connect as postgres user and run:
CREATE DATABASE withu_db;
CREATE DATABASE withu_dev_db;
```

### 3. Update Connection Strings

Update the password in the connection strings:

**appsettings.json:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=withu_db;Username=postgres;Password=YOUR_ACTUAL_PASSWORD"
  }
}
```

**appsettings.Development.json:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=withu_dev_db;Username=postgres;Password=YOUR_ACTUAL_PASSWORD;Port=5432"
  }
}
```

### 4. Install Dependencies and Run Migrations

```bash
# Navigate to the API directory
cd WithU.API

# Restore packages (this will install the PostgreSQL provider)
dotnet restore

# Create initial migration (if not exists)
dotnet ef migrations add InitialCreate

# Apply migrations to create tables
dotnet ef database update

# Run the application
dotnet run
```

## Connection String Parameters

- **Host**: PostgreSQL server address (localhost for local development)
- **Database**: Database name (withu_db or withu_dev_db)
- **Username**: PostgreSQL username (postgres by default)
- **Password**: Your PostgreSQL password
- **Port**: PostgreSQL port (5432 by default)

## Troubleshooting

### Common Issues:

1. **Connection refused**: Make sure PostgreSQL service is running
2. **Authentication failed**: Check username and password
3. **Database does not exist**: Create the database first
4. **Port issues**: Ensure port 5432 is not blocked by firewall

### Useful Commands:

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Restart PostgreSQL
sudo systemctl restart postgresql

# Connect to specific database
psql -U postgres -d withu_db

# List all databases
psql -U postgres -l
```

## Security Notes

- Change the default postgres password in production
- Consider creating a dedicated user for the application
- Use environment variables for sensitive connection string data
- Enable SSL for production deployments
