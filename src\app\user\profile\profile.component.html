<div class="profile-container">
  <div *ngIf="loading" class="loading-indicator">
    <p>Loading profile data...</p>
  </div>

  <div *ngIf="error" class="error-message">
    <p>{{ error }}</p>
  </div>

  <div *ngIf="success" class="success-message">
    <p>{{ success }}</p>
  </div>

  <div *ngIf="!loading && user" class="profile-content">
    <div class="profile-header">
      <h2>My Profile</h2>
      <div class="profile-actions">
        <button *ngIf="!editMode" (click)="toggleEditMode()" class="btn btn-secondary">Edit Profile</button>
        <button (click)="logout()" class="btn btn-outline">Logout</button>
      </div>
    </div>

    <div class="profile-card">
      <div class="profile-info">
        <div class="profile-avatar">
          <img [src]="user.profileImage || 'https://ui-avatars.com/api/?name=' + user.firstName + '+' + user.lastName + '&background=3b82f6&color=fff'" [alt]="user.firstName + ' ' + user.lastName">
        </div>

        <div class="profile-details">
          <h3>{{ user.firstName }} {{ user.lastName }}</h3>
          <p class="profile-role">{{ user.role }}</p>

          <div *ngIf="user.companyName || user.jobTitle" class="profile-company">
            <span *ngIf="user.jobTitle">{{ user.jobTitle }}</span>
            <span *ngIf="user.jobTitle && user.companyName"> at </span>
            <span *ngIf="user.companyName">{{ user.companyName }}</span>
          </div>

          <p class="profile-email">{{ user.email }}</p>
        </div>
      </div>

      <form *ngIf="editMode" [formGroup]="profileForm" (ngSubmit)="onSubmit()" class="profile-form">
        <div class="form-section">
          <h4 class="section-title">Personal Information</h4>

          <div class="form-row">
            <div class="form-group">
              <label for="firstName">First Name *</label>
              <input
                type="text"
                id="firstName"
                formControlName="firstName"
                class="form-control"
                [class.is-invalid]="profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched"
              >
              <div *ngIf="profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched" class="error-feedback">
                <span *ngIf="profileForm.get('firstName')?.errors?.['required']">First name is required.</span>
                <span *ngIf="profileForm.get('firstName')?.errors?.['minlength']">First name must be at least 2 characters.</span>
              </div>
            </div>

            <div class="form-group">
              <label for="lastName">Last Name *</label>
              <input
                type="text"
                id="lastName"
                formControlName="lastName"
                class="form-control"
                [class.is-invalid]="profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched"
              >
              <div *ngIf="profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched" class="error-feedback">
                <span *ngIf="profileForm.get('lastName')?.errors?.['required']">Last name is required.</span>
                <span *ngIf="profileForm.get('lastName')?.errors?.['minlength']">Last name must be at least 2 characters.</span>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="form-control"
              [disabled]="true"
            >
            <small class="form-text">Email cannot be changed.</small>
          </div>

          <div class="form-group">
            <label for="phoneNumber">Phone Number</label>
            <input
              type="tel"
              id="phoneNumber"
              formControlName="phoneNumber"
              class="form-control"
              placeholder="(*************"
            >
          </div>

          <div class="form-group">
            <label for="profileImage">Profile Image URL</label>
            <input
              type="url"
              id="profileImage"
              formControlName="profileImage"
              class="form-control"
              placeholder="https://example.com/image.jpg"
              [class.is-invalid]="profileForm.get('profileImage')?.invalid && profileForm.get('profileImage')?.touched"
            >
            <div *ngIf="profileForm.get('profileImage')?.invalid && profileForm.get('profileImage')?.touched" class="error-feedback">
              <span *ngIf="profileForm.get('profileImage')?.errors?.['pattern']">Please enter a valid URL.</span>
            </div>
            <small class="form-text">Enter a URL for your profile image.</small>
          </div>
        </div>

        <div class="form-section">
          <h4 class="section-title">Professional Information</h4>

          <div class="form-row">
            <div class="form-group">
              <label for="companyName">Company Name</label>
              <input
                type="text"
                id="companyName"
                formControlName="companyName"
                class="form-control"
              >
            </div>

            <div class="form-group">
              <label for="jobTitle">Job Title</label>
              <input
                type="text"
                id="jobTitle"
                formControlName="jobTitle"
                class="form-control"
              >
            </div>
          </div>

          <div class="form-group">
            <label for="bio">Bio</label>
            <textarea
              id="bio"
              formControlName="bio"
              class="form-control"
              rows="3"
              placeholder="Tell us about yourself..."
            ></textarea>
          </div>

          <div class="form-group">
            <label for="skills">Skills</label>
            <input
              type="text"
              id="skills"
              formControlName="skills"
              class="form-control"
              placeholder="e.g., Leadership, Communication, Project Management"
            >
            <small class="form-text">Comma-separated list of skills.</small>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn btn-outline" (click)="cancelEdit()">Cancel</button>
          <button type="submit" class="btn btn-primary" [disabled]="profileForm.invalid || loading">
            {{ loading ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </form>
    </div>

    <div class="participation-section">
      <h3>My Project Participations</h3>

      <div *ngIf="loadingParticipations" class="loading-indicator">
        <p>Loading participation data...</p>
      </div>

      <div *ngIf="!loadingParticipations && participations.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <h4>No Participations Yet</h4>
        <p>You haven't joined any projects yet. Browse projects to find opportunities to participate.</p>
        <a routerLink="/projects" class="btn btn-primary">Browse Projects</a>
      </div>

      <div *ngIf="!loadingParticipations && participations.length > 0" class="participation-list">
        <div *ngFor="let participation of participations" class="participation-card">
          <div class="participation-header">
            <h4 class="project-title">{{ participation.projectTitle }}</h4>
            <div class="participation-status" [ngClass]="getStatusClass(participation.status)">
              {{ participation.status }}
            </div>
          </div>

          <div class="participation-details">
            <div class="detail-item">
              <span class="detail-label">Joined:</span>
              <span class="detail-value">{{ participation.joinedAt | date:'mediumDate' }}</span>
            </div>

            <div *ngIf="participation.attendedAt" class="detail-item">
              <span class="detail-label">Attended:</span>
              <span class="detail-value">{{ participation.attendedAt | date:'mediumDate' }}</span>
            </div>

            <div *ngIf="participation.cancelledAt" class="detail-item">
              <span class="detail-label">Cancelled:</span>
              <span class="detail-value">{{ participation.cancelledAt | date:'mediumDate' }}</span>
            </div>

            <div *ngIf="participation.hoursLogged" class="detail-item">
              <span class="detail-label">Hours Logged:</span>
              <span class="detail-value">{{ participation.hoursLogged }}</span>
            </div>
          </div>

          <div *ngIf="participation.feedback" class="participation-feedback">
            <span class="feedback-label">Feedback:</span>
            <p class="feedback-text">{{ participation.feedback }}</p>
          </div>

          <div class="participation-actions">
            <a [routerLink]="['/projects', participation.projectId]" class="btn btn-outline">View Project</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
