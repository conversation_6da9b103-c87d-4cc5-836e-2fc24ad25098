<div class="project-form-container">
  <div class="form-header">
    <h2>{{ isEditMode ? 'Edit Project' : 'Create New Project' }}</h2>
    <p class="form-subtitle">{{ isEditMode ? 'Update the details of your community service project.' : 'Fill in the details to create a new community service project.' }}</p>
  </div>

  <div *ngIf="loading" class="loading-indicator">
    <p>Loading...</p>
  </div>

  <div *ngIf="error" class="error-message">
    <p>{{ error }}</p>
  </div>

  <div *ngIf="success" class="success-message">
    <p>{{ success }}</p>
  </div>

  <form [formGroup]="projectForm" (ngSubmit)="onSubmit()" class="project-form">
    <!-- Basic Information Section -->
    <div class="form-section">
      <h3 class="section-title">Basic Information</h3>
      
      <div class="form-group">
        <label for="title">Project Title *</label>
        <input 
          type="text" 
          id="title" 
          formControlName="title" 
          class="form-control" 
          [class.is-invalid]="projectForm.get('title')?.invalid && projectForm.get('title')?.touched"
        >
        <div *ngIf="projectForm.get('title')?.invalid && projectForm.get('title')?.touched" class="error-feedback">
          <span *ngIf="projectForm.get('title')?.errors?.['required']">Title is required.</span>
          <span *ngIf="projectForm.get('title')?.errors?.['minlength']">Title must be at least 3 characters.</span>
          <span *ngIf="projectForm.get('title')?.errors?.['maxlength']">Title cannot exceed 100 characters.</span>
        </div>
      </div>

      <div class="form-group">
        <label for="description">Description *</label>
        <textarea 
          id="description" 
          formControlName="description" 
          class="form-control" 
          rows="5"
          [class.is-invalid]="projectForm.get('description')?.invalid && projectForm.get('description')?.touched"
        ></textarea>
        <div *ngIf="projectForm.get('description')?.invalid && projectForm.get('description')?.touched" class="error-feedback">
          <span *ngIf="projectForm.get('description')?.errors?.['required']">Description is required.</span>
          <span *ngIf="projectForm.get('description')?.errors?.['maxlength']">Description cannot exceed 1000 characters.</span>
        </div>
      </div>

      <div class="form-group">
        <label for="image">Image URL</label>
        <input 
          type="url" 
          id="image" 
          formControlName="image" 
          class="form-control"
          placeholder="https://example.com/image.jpg"
          [class.is-invalid]="projectForm.get('image')?.invalid && projectForm.get('image')?.touched"
        >
        <div *ngIf="projectForm.get('image')?.invalid && projectForm.get('image')?.touched" class="error-feedback">
          <span *ngIf="projectForm.get('image')?.errors?.['pattern']">Please enter a valid URL.</span>
        </div>
        <small class="form-text text-muted">Enter a URL for an image that represents your project.</small>
      </div>
    </div>

    <!-- Project Details Section -->
    <div class="form-section">
      <h3 class="section-title">Project Details</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label for="location">Location *</label>
          <input 
            type="text" 
            id="location" 
            formControlName="location" 
            class="form-control"
            [class.is-invalid]="projectForm.get('location')?.invalid && projectForm.get('location')?.touched"
          >
          <div *ngIf="projectForm.get('location')?.invalid && projectForm.get('location')?.touched" class="error-feedback">
            <span *ngIf="projectForm.get('location')?.errors?.['required']">Location is required.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="date">Date *</label>
          <input 
            type="date" 
            id="date" 
            formControlName="date" 
            class="form-control"
            [class.is-invalid]="projectForm.get('date')?.invalid && projectForm.get('date')?.touched"
          >
          <div *ngIf="projectForm.get('date')?.invalid && projectForm.get('date')?.touched" class="error-feedback">
            <span *ngIf="projectForm.get('date')?.errors?.['required']">Date is required.</span>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="status">Status *</label>
          <select 
            id="status" 
            formControlName="status" 
            class="form-control"
            [class.is-invalid]="projectForm.get('status')?.invalid && projectForm.get('status')?.touched"
          >
            <option *ngFor="let option of statusOptions" [value]="option">{{ option }}</option>
          </select>
          <div *ngIf="projectForm.get('status')?.invalid && projectForm.get('status')?.touched" class="error-feedback">
            <span *ngIf="projectForm.get('status')?.errors?.['required']">Status is required.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="category">Category *</label>
          <select 
            id="category" 
            formControlName="category" 
            class="form-control"
            [class.is-invalid]="projectForm.get('category')?.invalid && projectForm.get('category')?.touched"
          >
            <option *ngFor="let option of categoryOptions" [value]="option">{{ option }}</option>
          </select>
          <div *ngIf="projectForm.get('category')?.invalid && projectForm.get('category')?.touched" class="error-feedback">
            <span *ngIf="projectForm.get('category')?.errors?.['required']">Category is required.</span>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="companyName">Company Name</label>
          <input 
            type="text" 
            id="companyName" 
            formControlName="companyName" 
            class="form-control"
          >
        </div>

        <div class="form-group">
          <label for="maxParticipants">Maximum Participants</label>
          <input 
            type="number" 
            id="maxParticipants" 
            formControlName="maxParticipants" 
            class="form-control"
            min="0"
            [class.is-invalid]="projectForm.get('maxParticipants')?.invalid && projectForm.get('maxParticipants')?.touched"
          >
          <div *ngIf="projectForm.get('maxParticipants')?.invalid && projectForm.get('maxParticipants')?.touched" class="error-feedback">
            <span *ngIf="projectForm.get('maxParticipants')?.errors?.['min']">Maximum participants cannot be negative.</span>
          </div>
          <small class="form-text text-muted">Enter 0 for unlimited participants.</small>
        </div>
      </div>

      <div class="form-group">
        <label for="skillsRequired">Skills Required</label>
        <input 
          type="text" 
          id="skillsRequired" 
          formControlName="skillsRequired" 
          class="form-control"
          placeholder="e.g., Gardening, Teaching, Cooking"
        >
        <small class="form-text text-muted">Comma-separated list of skills that would be helpful for this project.</small>
      </div>
    </div>

    <!-- Contact Information Section -->
    <div class="form-section">
      <h3 class="section-title">Contact Information</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label for="contactEmail">Contact Email</label>
          <input 
            type="email" 
            id="contactEmail" 
            formControlName="contactEmail" 
            class="form-control"
            [class.is-invalid]="projectForm.get('contactEmail')?.invalid && projectForm.get('contactEmail')?.touched"
          >
          <div *ngIf="projectForm.get('contactEmail')?.invalid && projectForm.get('contactEmail')?.touched" class="error-feedback">
            <span *ngIf="projectForm.get('contactEmail')?.errors?.['email']">Please enter a valid email address.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="contactPhone">Contact Phone</label>
          <input 
            type="tel" 
            id="contactPhone" 
            formControlName="contactPhone" 
            class="form-control"
            placeholder="(*************"
          >
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <button type="button" class="btn btn-secondary" (click)="cancel()">Cancel</button>
      <button type="button" class="btn btn-outline" (click)="resetForm()">Reset</button>
      <button type="submit" class="btn btn-primary" [disabled]="loading">
        {{ isEditMode ? 'Update Project' : 'Create Project' }}
      </button>
    </div>
  </form>
</div>
