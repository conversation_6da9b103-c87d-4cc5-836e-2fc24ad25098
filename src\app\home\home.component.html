<div class="home-container">
  <!-- Hero Section (Enhanced with explanation) -->
  <section class="hero" id="home">
    <div class="hero-content">
      <div class="hero-tag">FOR COMPANIES & EMPLOYEES</div>
      <h1>Community service that makes a real difference</h1>
      <p class="hero-subtitle">Build a platform for your organization to plan and manage efforts that motivate employees to serve community needs through employer leadership.</p>
      <div class="cta-buttons vertical">
        <a href="#features" class="btn btn-primary">Get Started</a>
        <a href="#about" class="btn btn-secondary">Learn More</a>
      </div>
    </div>
    <div class="hero-image">
      <div class="hero-visual">
        <img src="https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Community Volunteers" class="main-image">
        <div class="image-overlay"></div>
        <div class="impact-counter">
          <div class="counter-value">25,000+</div>
          <div class="counter-label">Volunteer Hours</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section (Enhanced with links) -->
  <section class="features" id="features">
    <div class="section-header">
      <div class="section-tag">A FRESH APPROACH TO COMMUNITY SERVICE</div>
      <h2>Say goodbye to disorganized volunteer efforts</h2>
      <p class="section-subtitle">Unlike traditional volunteer programs, Virtual Community Support puts structure and purpose at the center, creating meaningful impact for both employees and communities.</p>
    </div>

    <div class="feature-cards">
      <div class="feature-card">
        <div class="feature-icon">🏢</div>
        <h3>Companies</h3>
        <p>Register your organization and create community service projects that align with your company values</p>

      </div>
      <div class="feature-card">
        <div class="feature-icon">👥</div>
        <h3>Employees</h3>
        <p>Discover and join projects that match your interests, skills, and schedule for a fulfilling experience</p>

      </div>
      <div class="feature-card">
        <div class="feature-icon">🌍</div>
        <h3>Communities</h3>
        <p>Benefit from organized, targeted volunteer efforts that address real needs in your community</p>

      </div>
    </div>
  </section>

  <!-- About Section (New) -->
  <section class="about" id="about">
    <div class="section-header">
      <div class="section-tag">ABOUT WITHU</div>
      <h2>Our mission is to connect and empower</h2>
      <p class="section-subtitle">WithU is a platform that bridges the gap between companies, employees, and communities to create meaningful volunteer experiences and lasting positive impact.</p>
    </div>

    <div class="about-content">
      <div class="about-image">
        <img src="https://images.pexels.com/photos/3184433/pexels-photo-3184433.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Team collaboration">
      </div>
      <div class="about-text">
        <h3>Why WithU?</h3>
        <p>In today's world, companies are increasingly recognizing the importance of corporate social responsibility and employee engagement in community service. WithU provides the perfect platform to organize, manage, and track these efforts.</p>

        <h3>Our Values</h3>
        <ul class="values-list">
          <li><span class="value-icon">✨</span> <strong>Connection</strong> - Building bridges between organizations and communities</li>
          <li><span class="value-icon">🤝</span> <strong>Collaboration</strong> - Working together to achieve greater impact</li>
          <li><span class="value-icon">💡</span> <strong>Innovation</strong> - Finding new ways to address community needs</li>
          <li><span class="value-icon">♻️</span> <strong>Sustainability</strong> - Creating lasting positive change</li>
        </ul>

        <a href="#join" class="btn btn-primary">Join Our Mission</a>
      </div>
    </div>
  </section>

  <!-- Projects Section (Enhanced with IDs) -->
  <section class="projects" id="projects">
    <div class="section-header">
      <div class="section-tag">MAKE A DIFFERENCE</div>
      <h2>Featured Community Projects</h2>
      <p class="section-subtitle">Join these upcoming projects and make a real impact in your community while connecting with colleagues and developing new skills.</p>
    </div>

    <div *ngIf="loading" class="loading-indicator">
      <p>Loading projects...</p>
    </div>

    <div *ngIf="error" class="error-message">
      <p>{{ error }}</p>
    </div>

    <ng-container *ngIf="!loading && !error && projects.length > 0">
      <!-- Featured Project -->
      <div class="project-showcase">
        <div class="project-image-large" [style.background-image]="'url(' + projects[0].image + ')'" style="background-size: cover; background-position: center;">
          <div class="project-badge">Featured</div>
        </div>
        <div class="project-details-large">
          <h3>{{ projects[0].title }}</h3>
          <p class="project-description">{{ projects[0].description }}</p>
          <div class="project-meta">
            <div class="meta-item">
              <span class="meta-icon">📅</span>
              <span>{{ projects[0].date }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-icon">📍</span>
              <span>{{ projects[0].location }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-icon">👥</span>
              <span>{{ projects[0].participants }} participants</span>
            </div>
          </div>
          <a routerLink="/register" class="btn btn-primary" (click)="onJoinProject(projects[0].title)">Join Project</a>
        </div>
      </div>

      <!-- Project Cards -->
      <div class="project-cards">
        <div class="project-card" *ngFor="let project of projects.slice(1)">
          <div class="project-image" [style.background-image]="'url(' + project.image + ')'" style="background-size: cover; background-position: center;">
            <div class="project-category">Community</div>
          </div>
          <div class="project-content">
            <h3>{{ project.title }}</h3>
            <p>{{ project.description }}</p>
            <div class="project-details">
              <div class="detail-item">
                <span class="detail-icon">📅</span>
                <span>{{ project.date }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-icon">📍</span>
                <span>{{ project.location }}</span>
              </div>
            </div>
            <a routerLink="/register" class="btn btn-outline" (click)="onJoinProject(project.title)">Join Project</a>
          </div>
        </div>
      </div>
    </ng-container>

    <div class="view-all-projects">
      <a href="#all-projects" class="btn btn-secondary">View All Projects</a>
    </div>
  </section>

  <!-- Testimonials Section (Enhanced with ID) -->
  <section class="testimonials" id="testimonials">
    <div class="testimonial-wrapper">
      <div class="testimonial-highlight">
        <div class="quote-mark">❝</div>
        <h2 class="testimonial-headline">Virtual Community Support transformed our corporate volunteering program</h2>
        <p class="testimonial-subtext">After years of disorganized volunteer efforts, we switched to Virtual Community Support—and the difference is remarkable. The platform is excellent, and our employee engagement has increased by 78%.</p>
        <div class="quote-mark closing-mark">❞</div>
      </div>

      <div class="testimonial-grid">
        <div class="testimonial-card">
          <div class="testimonial-content">
            <div class="testimonial-rating">
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
            </div>
            <p class="testimonial-text">"This platform has transformed how our company approaches community service. It's now easier than ever to organize and participate in meaningful projects."</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar" style="background-image: url('https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'); background-size: cover;"></div>
            <div class="author-info">
              <p class="author-name">Sarah Johnson</p>
              <p class="author-title">HR Director, TechCorp</p>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <div class="testimonial-rating">
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
            </div>
            <p class="testimonial-text">"I've been able to contribute to causes I care about while connecting with colleagues from different departments. It's been a rewarding experience."</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar" style="background-image: url('https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'); background-size: cover;"></div>
            <div class="author-info">
              <p class="author-name">Michael Chen</p>
              <p class="author-title">Software Engineer, DataSystems</p>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <div class="testimonial-rating">
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
            </div>
            <p class="testimonial-text">"As a nonprofit partner, we've seen a significant increase in consistent volunteer support since local companies started using this platform."</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar" style="background-image: url('https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'); background-size: cover;"></div>
            <div class="author-info">
              <p class="author-name">Jessica Rivera</p>
              <p class="author-title">Director, Community Outreach Foundation</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- Features Grid Section (Enhanced with ID) -->
  <section class="features-grid" id="impact">
    <div class="section-header">
      <div class="section-tag">PACKED WITH FEATURES</div>
      <h2>Everything you need to manage community service</h2>
      <p class="section-subtitle">The complete toolkit for organizing, tracking, and maximizing the impact of your volunteer efforts.</p>
    </div>

    <div class="features-grid-container">
      <!-- Display services from API -->
      <div class="feature-item" *ngFor="let service of services">
        <div class="feature-icon">
          <img [src]="service.icon" [alt]="service.title + ' Icon'" style="width: 40px; height: 40px;">
        </div>
        <h3>{{ service.title }}</h3>
        <p>{{ service.description }}</p>
      </div>

      <!-- Fallback if no services are loaded -->
      <ng-container *ngIf="!services || services.length === 0">
        <div class="feature-item">
          <div class="feature-icon">📊</div>
          <h3>Project Dashboard</h3>
          <p>Track all your community service projects in one centralized location</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">👥</div>
          <h3>Volunteer Management</h3>
          <p>Easily organize volunteers, track hours, and manage skill matching</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">📱</div>
          <h3>Mobile App</h3>
          <p>Access your projects on the go with our dedicated mobile application</p>
        </div>
        <div class="feature-item">
          <div class="feature-icon">📅</div>
          <h3>Scheduling</h3>
          <p>Coordinate volunteer shifts and manage availability with ease</p>
        </div>
      </ng-container>
    </div>
  </section>


</div>
