import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ApiService, Project, User, ProjectParticipation } from '../../services/api.service';

@Component({
  selector: 'app-project-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './project-detail.component.html',
  styleUrl: './project-detail.component.scss'
})
export class ProjectDetailComponent implements OnInit {
  project?: Project;
  currentUser: User | null = null;
  userParticipation: ProjectParticipation | null = null;
  projectParticipations: ProjectParticipation[] = [];

  loading = false;
  loadingParticipation = false;
  loadingParticipations = false;
  joiningProject = false;
  leavingProject = false;

  error = '';
  participationError = '';
  participationSuccess = '';

  constructor(
    public apiService: ApiService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Get current user
    this.apiService.currentUser.subscribe(user => {
      this.currentUser = user;
    });

    this.route.params.subscribe(params => {
      if (params['id']) {
        const projectId = +params['id'];
        this.loadProject(projectId);

        // If user is logged in, check if they're participating in this project
        if (this.apiService.isLoggedIn() && this.currentUser) {
          this.checkUserParticipation(this.currentUser.id, projectId);
          this.loadProjectParticipations(projectId);
        }
      }
    });
  }

  loadProject(id: number): void {
    this.loading = true;
    this.apiService.getProjectById(id).subscribe({
      next: (project) => {
        this.project = project;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load project. Please try again.';
        this.loading = false;
        console.error('Error loading project:', err);
      }
    });
  }

  checkUserParticipation(userId: number, projectId: number): void {
    this.loadingParticipation = true;
    this.apiService.getUserProjectParticipation(userId, projectId).subscribe({
      next: (participation) => {
        this.userParticipation = participation;
        this.loadingParticipation = false;
      },
      error: (err) => {
        // 404 is expected if user is not participating
        if (err.status !== 404) {
          console.error('Error checking participation:', err);
        }
        this.loadingParticipation = false;
      }
    });
  }

  loadProjectParticipations(projectId: number): void {
    this.loadingParticipations = true;
    this.apiService.getProjectParticipations(projectId).subscribe({
      next: (response) => {
        this.projectParticipations = response.participations;
        this.loadingParticipations = false;
      },
      error: (err) => {
        console.error('Error loading project participations:', err);
        this.loadingParticipations = false;
      }
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Upcoming':
        return 'status-upcoming';
      case 'Active':
        return 'status-active';
      case 'Completed':
        return 'status-completed';
      case 'Cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  getParticipationStatusClass(status: string): string {
    switch (status) {
      case 'Registered':
        return 'status-registered';
      case 'Confirmed':
        return 'status-confirmed';
      case 'Attended':
        return 'status-attended';
      case 'Cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  joinProject(): void {
    if (!this.project) {
      return;
    }

    // Check if user is logged in
    if (!this.currentUser) {
      // Store the current URL to return after login
      const returnUrl = `/projects/${this.project.id}`;

      // Show a dialog asking if they want to login or register
      if (confirm('You need to be logged in to join this project. Would you like to sign up now? (Click OK for Sign Up, Cancel for Login)')) {
        this.router.navigate(['/register'], { queryParams: { returnUrl } });
      } else {
        this.router.navigate(['/login'], { queryParams: { returnUrl } });
      }
      return;
    }

    this.joiningProject = true;
    this.participationError = '';
    this.participationSuccess = '';

    console.log(`Joining project ${this.project.id} for user ${this.currentUser.id}`);

    this.apiService.joinProject(this.currentUser.id, this.project.id).subscribe({
      next: (participation) => {
        console.log('Successfully joined project:', participation);
        this.userParticipation = participation;
        this.participationSuccess = 'You have successfully joined this project!';
        this.joiningProject = false;

        // Reload project to update participant count
        this.loadProject(this.project!.id);
        this.loadProjectParticipations(this.project!.id);
      },
      error: (err) => {
        console.error('Error joining project:', err);
        this.participationError = err.error?.message || 'Failed to join project. Please try again.';
        this.joiningProject = false;
      }
    });
  }

  leaveProject(): void {
    if (!this.userParticipation || !this.project) return;

    if (confirm('Are you sure you want to leave this project?')) {
      this.leavingProject = true;
      this.participationError = '';
      this.participationSuccess = '';

      this.apiService.leaveProject(this.userParticipation.id).subscribe({
        next: () => {
          this.userParticipation = null;
          this.participationSuccess = 'You have successfully left this project.';
          this.leavingProject = false;

          // Reload project to update participant count
          this.loadProject(this.project!.id);
          this.loadProjectParticipations(this.project!.id);
        },
        error: (err) => {
          this.participationError = 'Failed to leave project. Please try again.';
          this.leavingProject = false;
          console.error('Error leaving project:', err);
        }
      });
    }
  }

  editProject(): void {
    if (this.project) {
      this.router.navigate(['/projects', this.project.id, 'edit']);
    }
  }

  deleteProject(): void {
    if (!this.project) return;

    if (confirm('Are you sure you want to delete this project?')) {
      this.apiService.deleteProject(this.project.id).subscribe({
        next: () => {
          this.router.navigate(['/projects']);
        },
        error: (err) => {
          this.error = 'Failed to delete project. Please try again.';
          console.error('Error deleting project:', err);
        }
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/projects']);
  }

  isProjectFull(): boolean {
    if (!this.project) return false;
    return this.project.maxParticipants > 0 && this.project.participants >= this.project.maxParticipants;
  }

  canJoinProject(): boolean {
    if (!this.project || !this.currentUser) return false;
    if (this.userParticipation) return false;
    if (this.project.status === 'Completed' || this.project.status === 'Cancelled') return false;
    if (this.isProjectFull()) return false;
    return true;
  }

  canLeaveProject(): boolean {
    if (!this.userParticipation) return false;
    if (this.userParticipation.status === 'Attended') return false;
    return true;
  }

  getJoinButtonText(): string {
    if (!this.project) return 'Join Project';

    if (this.project.status === 'Completed') return 'Project Completed';
    if (this.project.status === 'Cancelled') return 'Project Cancelled';
    if (this.isProjectFull()) return 'Project Full';
    if (this.userParticipation) {
      return `Joined (${this.userParticipation.status})`;
    }

    return 'Join This Project';
  }
}
