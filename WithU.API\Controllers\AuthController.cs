using Microsoft.AspNetCore.Mvc;
using WithU.API.Models;
using WithU.API.Services;

namespace WithU.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            _logger.LogInformation("Login request received for email: {Email}", request.Email);

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid login model state: {Errors}",
                    string.Join(", ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            try
            {
                _logger.LogInformation("Attempting to login user with email: {Email}", request.Email);
                var response = await _authService.LoginAsync(request);

                if (!response.Success)
                {
                    _logger.LogWarning("Login failed for email {Email}: {Message}", request.Email, response.Message);
                    return BadRequest(response);
                }

                _logger.LogInformation("Login successful for email: {Email}", request.Email);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for email {Email}: {Message}, Stack: {StackTrace}",
                    request.Email, ex.Message, ex.StackTrace);
                return StatusCode(500, new AuthResponse { Success = false, Message = $"An error occurred during login: {ex.Message}" });
            }
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            _logger.LogInformation("Registration request received for email: {Email}", request.Email);

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid model state: {Errors}",
                    string.Join(", ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            try
            {
                _logger.LogInformation("Attempting to register user with email: {Email}", request.Email);
                var response = await _authService.RegisterAsync(request);

                if (!response.Success)
                {
                    _logger.LogWarning("Registration failed for email {Email}: {Message}",
                        request.Email, response.Message);
                    return BadRequest(response);
                }

                _logger.LogInformation("Registration successful for email: {Email}", request.Email);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for email {Email}: {Message}, Stack: {StackTrace}",
                    request.Email, ex.Message, ex.StackTrace);
                return StatusCode(500, new AuthResponse { Success = false, Message = $"An error occurred during registration: {ex.Message}" });
            }
        }

        [HttpGet("user")]
        public async Task<IActionResult> GetCurrentUser()
        {
            try
            {
                var authHeader = Request.Headers["Authorization"].FirstOrDefault();
                if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    return Unauthorized(new { message = "No token provided" });
                }

                var token = authHeader.Substring("Bearer ".Length).Trim();
                var user = await _authService.GetCurrentUserAsync(token);
                if (user == null)
                {
                    return Unauthorized(new { message = "Invalid token" });
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current user");
                return StatusCode(500, new { message = "An error occurred while retrieving user information" });
            }
        }

        [HttpGet("test")]
        public IActionResult Test()
        {
            return Ok(new { message = "Auth API is working!", timestamp = DateTime.UtcNow });
        }

        [HttpPost("contact")]
        public IActionResult Contact([FromBody] ContactRequest request)
        {
            _logger.LogInformation("Contact request received from: {Name} ({Email})", request.Name, request.Email);

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid contact form data: {Errors}",
                    string.Join(", ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            try
            {
                // In a real application, you would:
                // 1. Save the message to database
                // 2. Send email notification to admin
                // 3. Send confirmation email to user

                _logger.LogInformation("Contact message processed successfully from {Name} ({Email}): {Message}",
                    request.Name, request.Email, request.Message.Substring(0, Math.Min(50, request.Message.Length)));

                return Ok(new ContactResponse
                {
                    Success = true,
                    Message = "Thank you for your message! We will get back to you soon."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing contact message from {Name} ({Email})", request.Name, request.Email);
                return StatusCode(500, new ContactResponse
                {
                    Success = false,
                    Message = "An error occurred while sending your message. Please try again."
                });
            }
        }
    }
}
