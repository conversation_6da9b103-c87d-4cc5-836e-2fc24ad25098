@echo off
echo Starting Virtual Community Support Application...

echo.
echo Starting backend server...
start cmd /k "cd server && npm run dev"

echo.
echo Waiting for server to start...
timeout /t 5 /nobreak > nul

echo.
echo Starting frontend application...
start cmd /k "ng serve --open"

echo.
echo Application started!
echo.
echo Backend: http://localhost:3000
echo Frontend: http://localhost:4200
echo.
echo Note: Authentication and database features have been removed.
echo The application now uses a simplified API with static data.
echo.
echo Press any key to exit this window...
pause > nul
