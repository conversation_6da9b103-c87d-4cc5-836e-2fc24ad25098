using WithU.API.Models;

namespace WithU.API.Repositories
{
    public interface IProjectParticipationRepository
    {
        Task<IEnumerable<ProjectParticipation>> GetAllAsync();
        Task<ProjectParticipation?> GetByIdAsync(int id);
        Task<IEnumerable<ProjectParticipation>> GetByUserIdAsync(int userId);
        Task<IEnumerable<ProjectParticipation>> GetByProjectIdAsync(int projectId);
        Task<ProjectParticipation?> GetByUserAndProjectIdAsync(int userId, int projectId);
        Task<ProjectParticipation> CreateAsync(ProjectParticipation participation);
        Task<ProjectParticipation?> UpdateAsync(int id, ProjectParticipation participation);
        Task<bool> DeleteAsync(int id);
        Task<int> GetParticipantCountForProjectAsync(int projectId);
    }
}
