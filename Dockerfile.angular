# Build stage
FROM node:18 AS build
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Update the API URL for production
RUN sed -i 's|http://localhost:5080/api|/api|g' src/app/services/api.service.ts

# Build the application
RUN npm run build -- --configuration production

# Production stage
FROM nginx:alpine
COPY --from=build /app/dist/virtual-community-support/browser /usr/share/nginx/html

# Configure nginx to handle Angular routing
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
