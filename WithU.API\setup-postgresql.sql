-- PostgreSQL Database Setup Script for WithU Application
-- Run this script as a PostgreSQL superuser (e.g., postgres)

-- Create the database for production
CREATE DATABASE withu_db
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Create the database for development
CREATE DATABASE withu_dev_db
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Optional: Create a dedicated user for the application
-- CREATE USER withu_user WITH PASSWORD 'your_secure_password_here';
-- GRANT ALL PRIVILEGES ON DATABASE withu_db TO withu_user;
-- GRANT ALL PRIVILEGES ON DATABASE withu_dev_db TO withu_user;

-- Grant necessary permissions
GRANT ALL ON DATABASE withu_db TO postgres;
GRANT ALL ON DATABASE withu_dev_db TO postgres;
