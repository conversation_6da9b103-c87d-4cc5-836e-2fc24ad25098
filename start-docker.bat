@echo off
echo ===================================================
echo       STARTING WITHU APPLICATION WITH DOCKER
echo ===================================================
echo.

echo [1/3] Checking prerequisites...
where docker >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Docker not found. Please install Docker Desktop.
    echo Download from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo [2/3] Building and starting containers...
docker-compose up -d --build

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to start Docker containers.
    pause
    exit /b 1
)

echo [3/3] Opening application in browser...
timeout /t 5 /nobreak >nul
start http://localhost:4200

echo.
echo ===================================================
echo       WITHU APPLICATION STARTED WITH DOCKER
echo ===================================================
echo.
echo API: http://localhost:5080
echo Frontend: http://localhost:4200
echo.
echo Your browser should open automatically to http://localhost:4200
echo.
echo To stop the application, run: docker-compose down
echo.
echo Press any key to exit this window...
pause >nul
