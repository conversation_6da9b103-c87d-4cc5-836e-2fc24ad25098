FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["WithU.API.csproj", "./"]
RUN dotnet restore "WithU.API.csproj"
COPY . .
RUN dotnet build "WithU.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "WithU.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "WithU.API.dll"]
