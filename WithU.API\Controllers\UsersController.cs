using Microsoft.AspNetCore.Mvc;
using WithU.API.Models;
using WithU.API.Repositories;

namespace WithU.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly IUserRepository _userRepository;
        private readonly IProjectParticipationRepository _participationRepository;
        private readonly ILogger<UsersController> _logger;

        public UsersController(
            IUserRepository userRepository,
            IProjectParticipationRepository participationRepository,
            ILogger<UsersController> logger)
        {
            _userRepository = userRepository;
            _participationRepository = participationRepository;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetUsers()
        {
            try
            {
                var users = await _userRepository.GetAllAsync();
                return Ok(new { users });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users");
                return StatusCode(500, "An error occurred while retrieving users");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(int id)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null)
                {
                    return NotFound($"User with ID {id} not found");
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user with ID {Id}", id);
                return StatusCode(500, $"An error occurred while retrieving user with ID {id}");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(int id, [FromBody] ProfileUpdateRequest request)
        {
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid model state: {Errors}", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ModelState);
            }

            try
            {
                // Get the existing user
                var existingUser = await _userRepository.GetByIdAsync(id);
                if (existingUser == null)
                {
                    return NotFound($"User with ID {id} not found");
                }

                // Update only the fields that are provided
                if (!string.IsNullOrEmpty(request.FirstName))
                    existingUser.FirstName = request.FirstName;

                if (!string.IsNullOrEmpty(request.LastName))
                    existingUser.LastName = request.LastName;

                if (!string.IsNullOrEmpty(request.Email))
                    existingUser.Email = request.Email;

                if (request.PhoneNumber != null)
                    existingUser.PhoneNumber = request.PhoneNumber;

                if (request.ProfileImage != null)
                    existingUser.ProfileImage = request.ProfileImage;

                if (request.Bio != null)
                    existingUser.Bio = request.Bio;

                if (request.Skills != null)
                    existingUser.Skills = request.Skills;

                if (request.CompanyName != null)
                    existingUser.CompanyName = request.CompanyName;

                if (request.JobTitle != null)
                    existingUser.JobTitle = request.JobTitle;

                // Always update the timestamp
                existingUser.UpdatedAt = DateTime.UtcNow;

                _logger.LogInformation("Updating user with ID {Id}: {FirstName} {LastName}", id, existingUser.FirstName, existingUser.LastName);
                var updatedUser = await _userRepository.UpdateAsync(id, existingUser);

                if (updatedUser == null)
                {
                    return NotFound($"User with ID {id} not found");
                }

                _logger.LogInformation("User updated successfully: {Id}", id);
                return Ok(updatedUser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user with ID {Id}: {Message}", id, ex.Message);
                return StatusCode(500, $"An error occurred while updating user with ID {id}: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            try
            {
                var result = await _userRepository.DeleteAsync(id);
                if (!result)
                {
                    return NotFound($"User with ID {id} not found");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user with ID {Id}", id);
                return StatusCode(500, $"An error occurred while deleting user with ID {id}");
            }
        }

        [HttpGet("{id}/participations")]
        public async Task<IActionResult> GetUserParticipations(int id)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null)
                {
                    return NotFound($"User with ID {id} not found");
                }

                var participations = await _participationRepository.GetByUserIdAsync(id);
                return Ok(new { participations });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving participations for user with ID {Id}", id);
                return StatusCode(500, $"An error occurred while retrieving participations for user with ID {id}");
            }
        }
    }
}
