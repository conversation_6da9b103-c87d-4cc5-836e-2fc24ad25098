using Microsoft.AspNetCore.Mvc;
using WithU.API.Models;
using WithU.API.Repositories;

namespace WithU.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly IUserRepository _userRepository;
        private readonly IProjectParticipationRepository _participationRepository;
        private readonly ILogger<UsersController> _logger;

        public UsersController(
            IUserRepository userRepository,
            IProjectParticipationRepository participationRepository,
            ILogger<UsersController> logger)
        {
            _userRepository = userRepository;
            _participationRepository = participationRepository;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetUsers()
        {
            try
            {
                var users = await _userRepository.GetAllAsync();
                return Ok(new { users });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users");
                return StatusCode(500, "An error occurred while retrieving users");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(int id)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null)
                {
                    return NotFound($"User with ID {id} not found");
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user with ID {Id}", id);
                return StatusCode(500, $"An error occurred while retrieving user with ID {id}");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(int id, [FromBody] User user)
        {
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid model state: {Errors}", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ModelState);
            }

            if (id != user.Id)
            {
                return BadRequest("ID in the URL does not match the ID in the request body");
            }

            try
            {
                // Get the existing user to preserve fields that might not be in the request
                var existingUser = await _userRepository.GetByIdAsync(id);
                if (existingUser == null)
                {
                    return NotFound($"User with ID {id} not found");
                }

                // Preserve fields that shouldn't be changed by the client
                user.Password = existingUser.Password;
                user.Role = existingUser.Role;
                user.CreatedAt = existingUser.CreatedAt;
                user.UpdatedAt = DateTime.UtcNow;

                // Ensure we don't have null values for required fields
                if (string.IsNullOrEmpty(user.FirstName))
                    user.FirstName = existingUser.FirstName;

                if (string.IsNullOrEmpty(user.LastName))
                    user.LastName = existingUser.LastName;

                if (string.IsNullOrEmpty(user.Email))
                    user.Email = existingUser.Email;

                _logger.LogInformation("Updating user with ID {Id}: {FirstName} {LastName}", id, user.FirstName, user.LastName);
                var updatedUser = await _userRepository.UpdateAsync(id, user);

                if (updatedUser == null)
                {
                    return NotFound($"User with ID {id} not found");
                }

                _logger.LogInformation("User updated successfully: {Id}", id);
                return Ok(updatedUser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user with ID {Id}: {Message}", id, ex.Message);
                return StatusCode(500, $"An error occurred while updating user with ID {id}: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            try
            {
                var result = await _userRepository.DeleteAsync(id);
                if (!result)
                {
                    return NotFound($"User with ID {id} not found");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user with ID {Id}", id);
                return StatusCode(500, $"An error occurred while deleting user with ID {id}");
            }
        }

        [HttpGet("{id}/participations")]
        public async Task<IActionResult> GetUserParticipations(int id)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null)
                {
                    return NotFound($"User with ID {id} not found");
                }

                var participations = await _participationRepository.GetByUserIdAsync(id);
                return Ok(new { participations });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving participations for user with ID {Id}", id);
                return StatusCode(500, $"An error occurred while retrieving participations for user with ID {id}");
            }
        }
    }
}
