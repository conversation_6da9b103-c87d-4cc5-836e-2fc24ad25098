// Variables (Light Dark theme)
$primary-color: #3b82f6; // Blue
$secondary-color: #4b5563; // Gray
$accent-color: #6b7280; // Medium gray
$text-color: #1f2937; // Dark gray
$light-text: #9ca3af; // Light gray
$background-color: #f3f4f6; // Very light gray
$light-background: #ffffff; // White
$dark-background: #111827; // Very dark gray
$border-radius: 4px; // Smaller border radius
$box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); // Subtle shadow
$gradient-primary: none; // No gradient

// Global styles
:host {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  color: $text-color;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-top: 60px; // Height of the fixed header
  min-height: calc(100vh - 60px - 400px); // Adjust based on header and footer height

  // Ensure router-outlet content takes full width
  ::ng-deep > * {
    width: 100%;
    display: block;
    flex: 1;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 0.5rem;
  width: 100%;
}

.btn {
  display: inline-block;
  padding: 0.5rem 1.25rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-align: center;
  text-decoration: none;
  letter-spacing: 0.01em;
  line-height: 1.5;
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: #2b6cb0; /* Darker version of primary color */
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-secondary {
  background-color: transparent;
  color: $text-color;
  border: 1px solid $secondary-color;

  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
    border-color: #cbd5e0; /* Darker version of secondary color */
  }
}

// Header styles
.header {
  background-color: $light-background;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
  height: 60px;
  display: flex;
  align-items: center;

  &.scrolled {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  .container {
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  height: 50px;
  width: 100%;

  .site-title {
    a.title-link {
      text-decoration: none;
      color: $text-color;
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    .title-text {
      font-size: 1.8rem;
      font-weight: 800;
      letter-spacing: -0.02em;
      color: $primary-color;
      transition: all 0.3s ease;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 0;
        height: 3px;
        background-color: $primary-color;
        transition: width 0.3s ease;
      }
    }

    &:hover {
      .title-text {
        transform: translateY(-1px);

        &::after {
          width: 100%;
        }
      }
    }
  }

  .nav-menu {
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
  }

  .nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;

    li {
      margin: 0 1rem;
    }

    a.nav-link {
      color: $text-color;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 0;
      cursor: pointer;
      font-size: 0.9rem;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: 0;
        left: 0;
        background-color: $primary-color;
        transition: width 0.3s ease;
      }

      &:hover, &.active {
        color: $primary-color;

        &::after {
          width: 100%;
        }
      }
    }
  }

  .join-button {
    display: flex;
    margin-left: 1.5rem;
    gap: 0.75rem;

    .join-btn, .login-btn, .profile-btn {
      display: flex;
      align-items: center;
      padding: 0.6rem 1.5rem;
      font-size: 1rem;
      border-radius: 4px;
      font-weight: 600;
      transition: all 0.3s ease;
      text-decoration: none;
      justify-content: center;
      letter-spacing: 0.02em;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }

    .join-btn {
      background-color: $primary-color;
      color: white;
    }

    .login-btn, .profile-btn {
      background-color: $secondary-color;
      color: white;
    }
  }

  .mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;

    .bar {
      display: block;
      width: 24px;
      height: 2px;
      margin: 4px auto;
      background-color: $text-color;
      transition: all 0.3s ease;
      border-radius: 1px;
    }

    &.active {
      .bar:nth-child(1) {
        transform: translateY(6px) rotate(45deg);
      }

      .bar:nth-child(2) {
        opacity: 0;
      }

      .bar:nth-child(3) {
        transform: translateY(-6px) rotate(-45deg);
      }
    }
  }
}

// Footer styles
.footer {
  background-color: $light-background;
  border-top: 1px solid #e2e8f0; /* Darker version of background color */
  padding: 1.5rem 0 1rem;
  margin-top: 1.5rem;

  .footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .footer-section {
    .footer-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: $text-color;

      &.brand-title {
        font-size: 1.8rem;
        font-weight: 800;
        letter-spacing: -0.02em;
        color: $primary-color;
      }
    }

    p {
      color: $light-text;
      margin-bottom: 1rem;
    }

    &.contact-section {
      background-color: rgba($primary-color, 0.05);
      padding: 1rem;
      border-radius: $border-radius;
      box-shadow: $box-shadow;
    }

    .contact-info {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      margin-bottom: 1rem;

      .contact-item {
        display: flex;
        align-items: center;

        .contact-icon {
          width: 24px;
          height: 24px;
          margin-right: 1rem;
          object-fit: contain;
        }

        p {
          margin: 0;
          color: $text-color;
        }
      }
    }

    .simple-contact-form {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      background-color: $light-background;
      padding: 1rem;
      border-radius: $border-radius;
      box-shadow: $box-shadow;
      margin-top: 0.75rem;

      .form-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: $text-color;
      }

      .alert {
        padding: 0.75rem;
        margin-bottom: 1rem;
        border-radius: $border-radius;
        font-size: 0.9rem;

        &.alert-success {
          background-color: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }

        &.alert-error {
          background-color: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }
      }

      .contact-input {
        padding: 0.75rem;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: $border-radius;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
        transition: border-color 0.3s ease;

        &:focus {
          outline: none;
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.25);
        }

        &.error {
          border-color: #dc3545;

          &:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
          }
        }
      }

      .error-message {
        color: #dc3545;
        font-size: 0.8rem;
        margin-bottom: 0.75rem;
        margin-top: -0.25rem;
      }

      .contact-textarea {
        min-height: 100px;
        resize: vertical;
      }

      .contact-btn {
        margin-top: 0.5rem;
        align-self: flex-start;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          background-color: #2b6cb0; /* Darker version of primary color */
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        &:active:not(:disabled) {
          transform: translateY(0);
        }

        &:disabled {
          background-color: #6c757d;
          cursor: not-allowed;
          opacity: 0.6;
          transform: none;
          box-shadow: none;
        }
      }
    }

    .social-links {
      display: flex;

      .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background-color: white;
        border-radius: 50%;
        margin-right: 0.75rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-3px);
        }

        .social-icon {
          width: 20px;
          height: 20px;
          object-fit: contain;
        }
      }
    }
  }

  .footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;

    p {
      color: $light-text;
      font-size: 0.875rem;
    }
  }
}

// Responsive styles (Enhanced for all devices)

// Large desktop and TV screens
@media (min-width: 1440px) {
  .container {
    max-width: 1400px;
  }
}

// Standard desktop
@media (max-width: 1200px) {
  .footer-content {
    gap: 1.5rem;
  }
}

// Small desktop and tablets landscape
@media (max-width: 1024px) {
  .navbar {
    .nav-links li {
      margin: 0 0.75rem;
    }

    .auth-buttons {
      margin-left: 1rem;
    }
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

// Tablets portrait
@media (max-width: 768px) {
  .navbar {
    .nav-menu {
      position: fixed;
      left: -100%;
      top: 0;
      flex-direction: column;
      background-color: rgba($background-color, 0.98);
      backdrop-filter: blur(10px);
      width: 100%;
      height: 100vh;
      text-align: center;
      transition: all 0.4s ease;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      padding: 15vh 0;
      z-index: 99;
      justify-content: center;

      &.active {
        left: 0;
      }
    }

    .nav-links {
      flex-direction: column;

      li {
        margin: 3vh 0;
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.5s forwards;
        animation-delay: calc(0.1s * var(--i, 0));

        @for $i from 1 through 6 {
          &:nth-child(#{$i}) {
            --i: #{$i};
          }
        }
      }

      a.nav-link {
        font-size: 1.25rem;
        padding: 1vh 2vh;

        &::after {
          bottom: -1vh;
          height: 3px;
        }
      }
    }

    .join-button {
      margin: 5vh 0 0 0;
      display: flex;
      justify-content: center;
      width: 100%;

      .join-btn {
        width: 60%;
        max-width: 250px;
        padding: 1.2vh 0;
        font-size: 1.2rem;
        justify-content: center;
      }
    }

    .mobile-menu-toggle {
      display: block;
      position: relative;
      z-index: 1000;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .footer {
    padding: 3rem 0 1.5rem;

    .footer-content {
      grid-template-columns: 1fr;
      gap: 2rem;
      text-align: center;

      .social-links {
        justify-content: center;
      }

      .contact-section {
        .contact-info {
          align-items: center;

          .contact-item {
            justify-content: center;
          }
        }

        .simple-contact-form {
          .contact-btn {
            align-self: center;
            width: 100%;
            max-width: 200px;
          }
        }
      }
    }
  }
}

// Large mobile phones
@media (max-width: 576px) {
  .navbar {
    padding: 0.75rem 0;

    .logo-text {
      font-size: 1.25rem;
    }
  }

  .footer {
    padding: 2.5rem 0 1.5rem;
    margin-top: 2.5rem;

    .footer-section {
      .footer-title {
        font-size: 1.1rem;
        margin-bottom: 1rem;
      }
    }
  }
}

// Small mobile phones
@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .navbar {
    .site-title .title-text {
      font-size: 1rem;
    }

    .mobile-menu-toggle .bar {
      width: 22px;
    }
  }

  .footer {
    .social-link {
      width: 35px;
      height: 35px;
    }
  }
}

// Extra small devices
@media (max-width: 320px) {
  .navbar {
    .logo-text {
      font-size: 1rem;
    }
  }
}
