<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h2>Sign In</h2>
      <p>Welcome back! Please sign in to your account.</p>
    </div>
    
    <div *ngIf="error" class="error-message">
      <p>{{ error }}</p>
    </div>
    
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
      <div class="form-group">
        <label for="email">Email</label>
        <input 
          type="email" 
          id="email" 
          formControlName="email" 
          class="form-control" 
          [class.is-invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
          placeholder="<EMAIL>"
        >
        <div *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched" class="error-feedback">
          <span *ngIf="loginForm.get('email')?.errors?.['required']">Email is required.</span>
          <span *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email address.</span>
        </div>
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input 
          type="password" 
          id="password" 
          formControlName="password" 
          class="form-control" 
          [class.is-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
        >
        <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="error-feedback">
          <span *ngIf="loginForm.get('password')?.errors?.['required']">Password is required.</span>
          <span *ngIf="loginForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters.</span>
        </div>
      </div>
      
      <div class="form-actions">
        <button type="submit" class="btn btn-primary" [disabled]="loading">
          {{ loading ? 'Signing In...' : 'Sign In' }}
        </button>
      </div>
    </form>
    
    <div class="auth-footer">
      <p>Don't have an account? <a routerLink="/register">Sign Up</a></p>
    </div>
  </div>
</div>
