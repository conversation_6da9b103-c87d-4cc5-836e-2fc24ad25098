import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiService, Project, CreateProjectRequest } from '../../services/api.service';

@Component({
  selector: 'app-project-form',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './project-form.component.html',
  styleUrl: './project-form.component.scss'
})
export class ProjectFormComponent implements OnInit {
  projectForm!: FormGroup;
  isEditMode = false;
  projectId?: number;
  loading = false;
  error = '';
  success = '';

  // Status options
  statusOptions = ['Upcoming', 'Active', 'Completed', 'Cancelled'];

  // Category options
  categoryOptions = ['Community', 'Education', 'Environment', 'Health', 'Technology', 'Arts', 'Other'];

  constructor(
    private fb: FormBuilder,
    private apiService: ApiService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initForm();

    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.projectId = +params['id'];
        this.loadProject(this.projectId);
      }
    });
  }

  initForm(): void {
    this.projectForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      description: ['', [Validators.required, Validators.maxLength(1000)]],
      image: ['', [Validators.pattern('https?://.+')]],
      location: ['', [Validators.required]],
      date: ['', [Validators.required]],
      status: ['Upcoming', [Validators.required]],
      category: ['Community', [Validators.required]],
      companyName: [''],
      skillsRequired: [''],
      maxParticipants: [0, [Validators.min(0)]],
      contactEmail: ['', [Validators.email]],
      contactPhone: ['']
    });
  }

  loadProject(id: number): void {
    this.loading = true;
    this.apiService.getProjectById(id).subscribe({
      next: (project) => {
        this.projectForm.patchValue({
          title: project.title,
          description: project.description,
          image: project.image,
          location: project.location,
          date: project.date,
          status: project.status,
          category: project.category,
          companyName: project.companyName,
          skillsRequired: project.skillsRequired,
          maxParticipants: project.maxParticipants,
          contactEmail: project.contactEmail,
          contactPhone: project.contactPhone
        });
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load project. Please try again.';
        this.loading = false;
        console.error('Error loading project:', err);
      }
    });
  }

  onSubmit(): void {
    if (this.projectForm.invalid) {
      this.markFormGroupTouched(this.projectForm);
      return;
    }

    // Check if user is logged in
    if (!this.apiService.isLoggedIn()) {
      this.error = 'You must be logged in to create or edit projects.';

      // Store the current URL to return after login
      const returnUrl = this.isEditMode ? `/projects/edit/${this.projectId}` : '/projects/new';

      // Show a dialog asking if they want to login or register
      if (confirm('You need to be logged in to create or edit projects. Would you like to sign up now? (Click OK for Sign Up, Cancel for Login)')) {
        this.router.navigate(['/register'], { queryParams: { returnUrl } });
      } else {
        this.router.navigate(['/login'], { queryParams: { returnUrl } });
      }
      return;
    }

    this.loading = true;
    this.error = '';
    this.success = '';

    // Get form values
    const projectData: CreateProjectRequest = {
      ...this.projectForm.value,
      // Ensure we have values for all required fields
      title: this.projectForm.value.title || '',
      description: this.projectForm.value.description || '',
      image: this.projectForm.value.image || 'https://images.unsplash.com/photo-1517048676732-d65bc937f952',
      location: this.projectForm.value.location || '',
      date: this.projectForm.value.date || new Date().toISOString().split('T')[0],
      status: this.projectForm.value.status || 'Upcoming',
      category: this.projectForm.value.category || 'Other',
      companyName: this.projectForm.value.companyName || '',
      skillsRequired: this.projectForm.value.skillsRequired || '',
      maxParticipants: this.projectForm.value.maxParticipants || 10,
      contactEmail: this.projectForm.value.contactEmail || '',
      contactPhone: this.projectForm.value.contactPhone || ''
    };

    console.log('Submitting project data:', projectData);

    if (this.isEditMode && this.projectId) {
      // Update existing project
      this.apiService.updateProject(this.projectId, {
        id: this.projectId,
        ...projectData,
        participants: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }).subscribe({
        next: (project) => {
          console.log('Project updated successfully:', project);
          this.success = 'Project updated successfully!';
          this.loading = false;
          setTimeout(() => {
            this.router.navigate(['/projects', this.projectId]);
          }, 1500);
        },
        error: (err) => {
          this.error = 'Failed to update project: ' + (err.error?.message || err.message || 'Please try again.');
          this.loading = false;
          console.error('Error updating project:', err);
        }
      });
    } else {
      // Create new project
      this.apiService.createProject(projectData).subscribe({
        next: (project) => {
          console.log('Project created successfully:', project);
          this.success = 'Project created successfully!';
          this.loading = false;
          setTimeout(() => {
            this.router.navigate(['/projects', project.id]);
          }, 1500);
        },
        error: (err) => {
          this.error = 'Failed to create project: ' + (err.error?.message || err.message || 'Please try again.');
          this.loading = false;
          console.error('Error creating project:', err);
        }
      });
    }
  }

  // Helper method to mark all form controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  // Reset the form
  resetForm(): void {
    if (this.isEditMode && this.projectId) {
      this.loadProject(this.projectId);
    } else {
      this.projectForm.reset({
        status: 'Upcoming',
        category: 'Community',
        maxParticipants: 0
      });
    }
    this.error = '';
    this.success = '';
  }

  // Cancel and go back
  cancel(): void {
    if (this.isEditMode && this.projectId) {
      this.router.navigate(['/projects', this.projectId]);
    } else {
      this.router.navigate(['/projects']);
    }
  }
}
