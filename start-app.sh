#!/bin/bash

echo "Starting Virtual Community Support Application..."

echo ""
echo "Starting backend server..."
gnome-terminal -- bash -c "cd server && npm run dev; exec bash" ||
xterm -e "cd server && npm run dev; exec bash" ||
konsole --noclose -e "cd server && npm run dev" ||
terminal -e "cd server && npm run dev" ||
echo "Could not open a terminal window. Please start the server manually."

echo ""
echo "Waiting for server to start..."
sleep 5

echo ""
echo "Starting frontend application..."
gnome-terminal -- bash -c "ng serve --open; exec bash" ||
xterm -e "ng serve --open; exec bash" ||
konsole --noclose -e "ng serve --open" ||
terminal -e "ng serve --open" ||
echo "Could not open a terminal window. Please start the frontend manually."

echo ""
echo "Application started!"
echo ""
echo "Backend: http://localhost:3000"
echo "Frontend: http://localhost:4200"
echo ""
echo "Note: Authentication and database features have been removed."
echo "The application now uses a simplified API with static data."
echo ""
echo "Press Enter to exit this window..."
read
