using Microsoft.AspNetCore.Mvc;
using WithU.API.Models;
using WithU.API.Repositories;

namespace WithU.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ParticipationsController : ControllerBase
    {
        private readonly IProjectParticipationRepository _participationRepository;
        private readonly IProjectRepository _projectRepository;
        private readonly IUserRepository _userRepository;
        private readonly ILogger<ParticipationsController> _logger;

        public ParticipationsController(
            IProjectParticipationRepository participationRepository,
            IProjectRepository projectRepository,
            IUserRepository userRepository,
            ILogger<ParticipationsController> logger)
        {
            _participationRepository = participationRepository;
            _projectRepository = projectRepository;
            _userRepository = userRepository;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetParticipations()
        {
            try
            {
                var participations = await _participationRepository.GetAllAsync();
                return Ok(new { participations });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving participations");
                return StatusCode(500, "An error occurred while retrieving participations");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetParticipation(int id)
        {
            try
            {
                var participation = await _participationRepository.GetByIdAsync(id);
                if (participation == null)
                {
                    return NotFound($"Participation with ID {id} not found");
                }

                return Ok(participation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving participation with ID {Id}", id);
                return StatusCode(500, $"An error occurred while retrieving participation with ID {id}");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateParticipation([FromBody] ProjectParticipation participation)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                // Check if user exists
                var user = await _userRepository.GetByIdAsync(participation.UserId);
                if (user == null)
                {
                    return BadRequest($"User with ID {participation.UserId} not found");
                }

                // Check if project exists
                var project = await _projectRepository.GetByIdAsync(participation.ProjectId);
                if (project == null)
                {
                    return BadRequest($"Project with ID {participation.ProjectId} not found");
                }

                // Check if user is already participating in this project
                var existingParticipation = await _participationRepository.GetByUserAndProjectIdAsync(
                    participation.UserId, participation.ProjectId);
                
                if (existingParticipation != null)
                {
                    return BadRequest("User is already participating in this project");
                }

                // Check if project is at capacity
                if (project.MaxParticipants > 0)
                {
                    var currentParticipants = await _participationRepository.GetParticipantCountForProjectAsync(project.Id);
                    if (currentParticipants >= project.MaxParticipants)
                    {
                        return BadRequest("Project has reached maximum capacity");
                    }
                }

                // Create participation
                var createdParticipation = await _participationRepository.CreateAsync(participation);
                
                // Update project participant count
                project.Participants = await _participationRepository.GetParticipantCountForProjectAsync(project.Id);

                return CreatedAtAction(nameof(GetParticipation), new { id = createdParticipation.Id }, createdParticipation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating participation");
                return StatusCode(500, "An error occurred while creating the participation");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateParticipation(int id, [FromBody] ProjectParticipation participation)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (id != participation.Id)
            {
                return BadRequest("ID in the URL does not match the ID in the request body");
            }

            try
            {
                var updatedParticipation = await _participationRepository.UpdateAsync(id, participation);
                if (updatedParticipation == null)
                {
                    return NotFound($"Participation with ID {id} not found");
                }

                return Ok(updatedParticipation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating participation with ID {Id}", id);
                return StatusCode(500, $"An error occurred while updating participation with ID {id}");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteParticipation(int id)
        {
            try
            {
                var participation = await _participationRepository.GetByIdAsync(id);
                if (participation == null)
                {
                    return NotFound($"Participation with ID {id} not found");
                }

                var result = await _participationRepository.DeleteAsync(id);
                if (!result)
                {
                    return StatusCode(500, $"Failed to delete participation with ID {id}");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting participation with ID {Id}", id);
                return StatusCode(500, $"An error occurred while deleting participation with ID {id}");
            }
        }

        [HttpGet("project/{projectId}")]
        public async Task<IActionResult> GetParticipationsByProject(int projectId)
        {
            try
            {
                var project = await _projectRepository.GetByIdAsync(projectId);
                if (project == null)
                {
                    return NotFound($"Project with ID {projectId} not found");
                }

                var participations = await _participationRepository.GetByProjectIdAsync(projectId);
                return Ok(new { participations });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving participations for project with ID {Id}", projectId);
                return StatusCode(500, $"An error occurred while retrieving participations for project with ID {projectId}");
            }
        }

        [HttpGet("user/{userId}/project/{projectId}")]
        public async Task<IActionResult> GetParticipationByUserAndProject(int userId, int projectId)
        {
            try
            {
                var participation = await _participationRepository.GetByUserAndProjectIdAsync(userId, projectId);
                if (participation == null)
                {
                    return NotFound($"Participation for user {userId} in project {projectId} not found");
                }

                return Ok(participation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving participation for user {UserId} in project {ProjectId}", userId, projectId);
                return StatusCode(500, $"An error occurred while retrieving participation for user {userId} in project {projectId}");
            }
        }
    }
}
