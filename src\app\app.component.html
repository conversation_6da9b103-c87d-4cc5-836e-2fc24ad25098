<header class="header" [class.scrolled]="isScrolled">
  <nav class="navbar">
    <div class="container">
      <div class="site-title">
        <a routerLink="/" class="title-link">
          <span class="title-text">WithU</span>
        </a>
      </div>

      <div class="nav-menu" [class.active]="isMobileMenuOpen">
        <ul class="nav-links">
          <li><a routerLink="/" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}"
              class="nav-link">Home</a></li>
          <li><a routerLink="/" fragment="features" class="nav-link">Services</a></li>
          <li><a routerLink="/projects" routerLinkActive="active" class="nav-link">Projects</a></li>
          <li><a routerLink="/" fragment="about" class="nav-link">About</a></li>
          <li><a routerLink="/" fragment="contact" class="nav-link">Contact</a></li>
        </ul>

        <div class="join-button">
          <ng-container *ngIf="apiService.isLoggedIn(); else notLoggedIn">
            <a routerLink="/profile" class="btn btn-secondary profile-btn">My Profile</a>
            <a routerLink="/projects/new" class="btn btn-primary join-btn">Create Project</a>
          </ng-container>
          <ng-template #notLoggedIn>
            <a routerLink="/login" class="btn btn-secondary login-btn">Login</a>
            <a routerLink="/register" class="btn btn-primary join-btn">Sign Up</a>
          </ng-template>
        </div>
      </div>

      <button class="mobile-menu-toggle" [class.active]="isMobileMenuOpen" (click)="toggleMobileMenu()"
        aria-label="Toggle menu">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </button>
    </div>
  </nav>
</header>

<main>
  <router-outlet></router-outlet>
</main>

<footer id="contact" class="footer">
  <div class="container">
    <div class="footer-content">
      <div class="footer-section">
        <h3 class="footer-title brand-title">WithU</h3>
        <p>Empowering employees to serve community needs through employer leadership.</p>
        <div class="social-links">

          <a href="https://www.instagram.com/umesh.r.makwana/?igsh=MWFtNWdyNGlnZGsydw%3D%3D&utm_source=ig_contact_invite#"
            class="social-link">
            <img src="https://cdn-icons-png.flaticon.com/512/3955/3955024.png" alt="Instagram" class="social-icon">
          </a>
          <a href="E:\community2\src\app\user\image.png" class="social-link">
            <img src="https://cdn-icons-png.flaticon.com/512/3670/3670151.png" alt="Github" class="social-icon">
          </a>
          <a href="https://www.linkedin.com/in/makwana-umesh-1333a4312/?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
            class="social-link">
            <img src="https://cdn-icons-png.flaticon.com/512/3536/3536505.png" alt="LinkedIn" class="social-icon">
          </a>
        </div>
      </div>



      <div class="footer-section contact-section">
        <h3 class="footer-title">Contact Us</h3>

        <div class="contact-info">
          <div class="contact-item">
            <img src="https://cdn-icons-png.flaticon.com/512/2838/2838912.png" alt="Location" class="contact-icon">
            <p>123 Community Street<br>Volunteer City, VC 12345</p>
          </div>

          <div class="contact-item">
            <img src="https://cdn-icons-png.flaticon.com/512/3059/3059502.png" alt="Email" class="contact-icon">
            <p>info&#64;withu.org</p>
          </div>

          <div class="contact-item">
            <img src="https://cdn-icons-png.flaticon.com/512/455/455705.png" alt="Phone" class="contact-icon">
            <p>(*************</p>
          </div>
        </div>

        <div class="simple-contact-form">
          <h4 class="form-title">Send us a message</h4>

          <!-- Success Message -->
          <div *ngIf="contactSuccess" class="alert alert-success">
            {{ contactSuccess }}
          </div>

          <!-- Error Message -->
          <div *ngIf="contactError" class="alert alert-error">
            {{ contactError }}
          </div>

          <form [formGroup]="contactForm" (ngSubmit)="onContactSubmit()">
            <input
              type="text"
              placeholder="Your Name"
              class="contact-input"
              formControlName="name"
              [class.error]="contactForm.get('name')?.invalid && contactForm.get('name')?.touched"
            >
            <div *ngIf="contactForm.get('name')?.invalid && contactForm.get('name')?.touched" class="error-message">
              <span *ngIf="contactForm.get('name')?.errors?.['required']">Name is required.</span>
              <span *ngIf="contactForm.get('name')?.errors?.['minlength']">Name must be at least 2 characters.</span>
            </div>

            <input
              type="email"
              placeholder="Your Email"
              class="contact-input"
              formControlName="email"
              [class.error]="contactForm.get('email')?.invalid && contactForm.get('email')?.touched"
            >
            <div *ngIf="contactForm.get('email')?.invalid && contactForm.get('email')?.touched" class="error-message">
              <span *ngIf="contactForm.get('email')?.errors?.['required']">Email is required.</span>
              <span *ngIf="contactForm.get('email')?.errors?.['email']">Please enter a valid email address.</span>
            </div>

            <textarea
              placeholder="Your Message"
              class="contact-input contact-textarea"
              formControlName="message"
              [class.error]="contactForm.get('message')?.invalid && contactForm.get('message')?.touched"
            ></textarea>
            <div *ngIf="contactForm.get('message')?.invalid && contactForm.get('message')?.touched" class="error-message">
              <span *ngIf="contactForm.get('message')?.errors?.['required']">Message is required.</span>
              <span *ngIf="contactForm.get('message')?.errors?.['minlength']">Message must be at least 10 characters.</span>
            </div>

            <button
              type="submit"
              class="btn btn-primary contact-btn"
              [disabled]="contactLoading || contactForm.invalid"
            >
              <span *ngIf="contactLoading">Sending...</span>
              <span *ngIf="!contactLoading">Send Message</span>
            </button>
          </form>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <p>&copy; 2025 WithU. All rights reserved.</p>
    </div>
  </div>
</footer>