<header class="header" [class.scrolled]="isScrolled">
  <nav class="navbar">
    <div class="container">
      <div class="site-title">
        <a routerLink="/" class="title-link" (click)="navigateToHome()" title="Go to Home Page">
          <span class="title-text">WithU</span>
        </a>
      </div>

      <div class="nav-menu" [class.active]="isMobileMenuOpen">
        <ul class="nav-links">
          <li><a routerLink="/" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}"
              class="nav-link">Home</a></li>
          <li><a routerLink="/" fragment="features" class="nav-link">Services</a></li>
          <li><a routerLink="/projects" routerLinkActive="active" class="nav-link">Projects</a></li>
          <li><a routerLink="/" fragment="about" class="nav-link">About</a></li>
          <li><a routerLink="/" fragment="contact" class="nav-link">Contact</a></li>
        </ul>

        <div class="join-button">
          <ng-container *ngIf="apiService.isLoggedIn(); else notLoggedIn">
            <a routerLink="/profile" class="btn btn-secondary profile-btn">My Profile</a>
            <a routerLink="/projects/new" class="btn btn-primary join-btn">Create Project</a>
          </ng-container>
          <ng-template #notLoggedIn>
            <a routerLink="/login" class="btn btn-secondary login-btn">Login</a>
            <a routerLink="/register" class="btn btn-primary join-btn">Sign Up</a>
          </ng-template>

          <!-- Community Garden Button -->
          <div class="community-garden-section">
            <a routerLink="/login" class="btn btn-garden community-garden-btn" (click)="navigateToCommunityGarden()" title="Join our Community Garden">
              <svg class="garden-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                <path d="M12 16C16 16 20 20 20 20H4S8 16 12 16Z"/>
              </svg>
              <span>Community Garden</span>
            </a>
          </div>
        </div>
      </div>

      <button class="mobile-menu-toggle" [class.active]="isMobileMenuOpen" (click)="toggleMobileMenu()"
        aria-label="Toggle menu">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </button>
    </div>
  </nav>
</header>

<main>
  <router-outlet></router-outlet>
</main>

<footer id="contact" class="footer">
  <div class="container">
    <div class="footer-content">
      <div class="footer-section">
        <h3 class="footer-title brand-title">WithU</h3>
        <p>Empowering employees to serve community needs through employer leadership.</p>
        <div class="social-links">

          <a href="https://www.instagram.com/umesh.r.makwana/?igsh=MWFtNWdyNGlnZGsydw%3D%3D&utm_source=ig_contact_invite#"
            class="social-link">
            <img src="https://cdn-icons-png.flaticon.com/512/3955/3955024.png" alt="Instagram" class="social-icon">
          </a>
          <a href="E:\community2\src\app\user\image.png" class="social-link">
            <img src="https://cdn-icons-png.flaticon.com/512/3670/3670151.png" alt="Github" class="social-icon">
          </a>
          <a href="https://www.linkedin.com/in/makwana-umesh-1333a4312/?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
            class="social-link">
            <img src="https://cdn-icons-png.flaticon.com/512/3536/3536505.png" alt="LinkedIn" class="social-icon">
          </a>
        </div>
      </div>



      <div class="footer-section contact-section">
        <h3 class="footer-title">Get In Touch</h3>
        <p class="contact-subtitle">Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>

        <div class="contact-container">
          <!-- Contact Information -->
          <div class="contact-info-card">
            <h4 class="contact-info-title">Contact Information</h4>

            <div class="contact-info-grid">
              <div class="contact-item">
                <div class="contact-icon-wrapper">
                  <svg class="contact-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>
                </div>
                <div class="contact-details">
                  <h5>Address</h5>
                  <p>123 Community Street<br>Volunteer City, VC 12345</p>
                </div>
              </div>

              <div class="contact-item">
                <div class="contact-icon-wrapper">
                  <svg class="contact-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                </div>
                <div class="contact-details">
                  <h5>Email</h5>
                  <p><a href="mailto:info&#64;withu.org">info&#64;withu.org</a></p>
                </div>
              </div>

              <div class="contact-item">
                <div class="contact-icon-wrapper">
                  <svg class="contact-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                  </svg>
                </div>
                <div class="contact-details">
                  <h5>Phone</h5>
                  <p><a href="tel:+11234567890">(*************</a></p>
                </div>
              </div>

              <div class="contact-item">
                <div class="contact-icon-wrapper">
                  <svg class="contact-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12,6 12,12 16,14"></polyline>
                  </svg>
                </div>
                <div class="contact-details">
                  <h5>Business Hours</h5>
                  <p>Mon - Fri: 9:00 AM - 6:00 PM<br>Sat - Sun: 10:00 AM - 4:00 PM</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="contact-form-card">
            <h4 class="form-title">Send us a message</h4>

            <!-- Success Message -->
            <div *ngIf="contactSuccess" class="alert alert-success">
              <svg class="alert-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="20,6 9,17 4,12"></polyline>
              </svg>
              {{ contactSuccess }}
            </div>

            <!-- Error Message -->
            <div *ngIf="contactError" class="alert alert-error">
              <svg class="alert-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="15" y1="9" x2="9" y2="15"></line>
                <line x1="9" y1="9" x2="15" y2="15"></line>
              </svg>
              {{ contactError }}
            </div>

            <form [formGroup]="contactForm" (ngSubmit)="onContactSubmit()" class="contact-form">
              <div class="form-row">
                <div class="form-group">
                  <label for="contact-name">Full Name *</label>
                  <input
                    id="contact-name"
                    type="text"
                    placeholder="Enter your full name"
                    class="contact-input"
                    formControlName="name"
                    [class.error]="contactForm.get('name')?.invalid && contactForm.get('name')?.touched"
                  >
                  <div *ngIf="contactForm.get('name')?.invalid && contactForm.get('name')?.touched" class="error-message">
                    <span *ngIf="contactForm.get('name')?.errors?.['required']">Name is required.</span>
                    <span *ngIf="contactForm.get('name')?.errors?.['minlength']">Name must be at least 2 characters.</span>
                  </div>
                </div>

                <div class="form-group">
                  <label for="contact-email">Email Address *</label>
                  <input
                    id="contact-email"
                    type="email"
                    placeholder="Enter your email address"
                    class="contact-input"
                    formControlName="email"
                    [class.error]="contactForm.get('email')?.invalid && contactForm.get('email')?.touched"
                  >
                  <div *ngIf="contactForm.get('email')?.invalid && contactForm.get('email')?.touched" class="error-message">
                    <span *ngIf="contactForm.get('email')?.errors?.['required']">Email is required.</span>
                    <span *ngIf="contactForm.get('email')?.errors?.['email']">Please enter a valid email address.</span>
                  </div>
                </div>
              </div>

              <div class="form-group">
                <label for="contact-message">Message *</label>
                <textarea
                  id="contact-message"
                  placeholder="Tell us how we can help you..."
                  class="contact-input contact-textarea"
                  formControlName="message"
                  [class.error]="contactForm.get('message')?.invalid && contactForm.get('message')?.touched"
                  rows="5"
                ></textarea>
                <div *ngIf="contactForm.get('message')?.invalid && contactForm.get('message')?.touched" class="error-message">
                  <span *ngIf="contactForm.get('message')?.errors?.['required']">Message is required.</span>
                  <span *ngIf="contactForm.get('message')?.errors?.['minlength']">Message must be at least 10 characters.</span>
                </div>
              </div>

              <button
                type="submit"
                class="btn btn-primary contact-submit-btn"
                [disabled]="contactLoading || contactForm.invalid"
              >
                <svg *ngIf="contactLoading" class="loading-spinner" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                    <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                    <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                  </circle>
                </svg>
                <svg *ngIf="!contactLoading" class="send-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                </svg>
                <span *ngIf="contactLoading">Sending...</span>
                <span *ngIf="!contactLoading">Send Message</span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <p>&copy; 2025 WithU. All rights reserved.</p>
    </div>
  </div>
</footer>