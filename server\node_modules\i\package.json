{"name": "i", "version": "0.3.7", "author": "<PERSON><PERSON> <<EMAIL>> (pksunkara.github.com)", "description": "custom inflections for nodejs", "main": "./lib/inflect", "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "homepage": "http://pksunkara.github.com/inflect", "scripts": {"test": "./node_modules/.bin/vows --spec $(find test -name '*-test.js')"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {"vows": "^0.8.2"}, "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/pksunkara/inflect/raw/master/LICENSE"}]}