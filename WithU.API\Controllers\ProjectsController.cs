using Microsoft.AspNetCore.Mvc;
using WithU.API.Models;
using WithU.API.Repositories;

namespace WithU.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProjectsController : ControllerBase
    {
        private readonly ILogger<ProjectsController> _logger;
        private readonly IProjectRepository _projectRepository;

        public ProjectsController(ILogger<ProjectsController> logger, IProjectRepository projectRepository)
        {
            _logger = logger;
            _projectRepository = projectRepository;
        }

        // GET: api/projects
        [HttpGet]
        public async Task<IActionResult> GetProjects()
        {
            try
            {
                var projects = await _projectRepository.GetAllAsync();
                return Ok(new { projects });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving projects");
                return StatusCode(500, "An error occurred while retrieving projects");
            }
        }

        // GET: api/projects/{id}
        [HttpGet("{id}")]
        public async Task<IActionResult> GetProject(int id)
        {
            try
            {
                var project = await _projectRepository.GetByIdAsync(id);
                if (project == null)
                {
                    return NotFound($"Project with ID {id} not found");
                }

                return Ok(project);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving project with ID {Id}", id);
                return StatusCode(500, $"An error occurred while retrieving project with ID {id}");
            }
        }

        // POST: api/projects
        [HttpPost]
        public async Task<IActionResult> CreateProject([FromBody] Project project)
        {
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid model state: {Errors}", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ModelState);
            }

            try
            {
                // Set default values for required fields
                project.CreatedAt = DateTime.UtcNow;
                project.UpdatedAt = DateTime.UtcNow;
                project.Participants = 0;

                // Ensure we have valid values for required fields
                if (string.IsNullOrEmpty(project.Title))
                {
                    _logger.LogWarning("Project title is required");
                    return BadRequest("Project title is required");
                }

                if (string.IsNullOrEmpty(project.Description))
                {
                    _logger.LogWarning("Project description is required");
                    return BadRequest("Project description is required");
                }

                if (string.IsNullOrEmpty(project.Location))
                {
                    _logger.LogWarning("Project location is required");
                    return BadRequest("Project location is required");
                }

                if (string.IsNullOrEmpty(project.Date))
                {
                    project.Date = DateTime.UtcNow.ToString("yyyy-MM-dd");
                }

                if (string.IsNullOrEmpty(project.Status))
                    project.Status = "Upcoming";

                if (string.IsNullOrEmpty(project.Category))
                    project.Category = "Other";

                if (string.IsNullOrEmpty(project.Image))
                    project.Image = "https://images.unsplash.com/photo-1517048676732-d65bc937f952";

                if (project.MaxParticipants <= 0)
                    project.MaxParticipants = 10;

                _logger.LogInformation("Creating project: {Title}", project.Title);
                var createdProject = await _projectRepository.CreateAsync(project);
                _logger.LogInformation("Project created with ID: {Id}", createdProject.Id);

                return CreatedAtAction(nameof(GetProject), new { id = createdProject.Id }, createdProject);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating project: {Message}", ex.Message);
                return StatusCode(500, $"An error occurred while creating the project: {ex.Message}");
            }
        }

        // PUT: api/projects/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProject(int id, [FromBody] Project project)
        {
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid model state: {Errors}", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage));
                return BadRequest(ModelState);
            }

            if (id != project.Id)
            {
                return BadRequest("ID in the URL does not match the ID in the request body");
            }

            try
            {
                // Get the existing project to preserve fields that might not be in the request
                var existingProject = await _projectRepository.GetByIdAsync(id);
                if (existingProject == null)
                {
                    return NotFound($"Project with ID {id} not found");
                }

                // Preserve fields that shouldn't be changed by the client
                project.CreatedAt = existingProject.CreatedAt;
                project.UpdatedAt = DateTime.UtcNow;

                // Ensure we don't have null values for required fields
                if (string.IsNullOrEmpty(project.Title))
                    project.Title = existingProject.Title;

                if (string.IsNullOrEmpty(project.Description))
                    project.Description = existingProject.Description;

                if (string.IsNullOrEmpty(project.Location))
                    project.Location = existingProject.Location;

                if (string.IsNullOrEmpty(project.Date))
                    project.Date = existingProject.Date;

                if (string.IsNullOrEmpty(project.Status))
                    project.Status = existingProject.Status;

                if (string.IsNullOrEmpty(project.Category))
                    project.Category = existingProject.Category;

                if (string.IsNullOrEmpty(project.Image))
                    project.Image = existingProject.Image;

                if (project.MaxParticipants <= 0)
                    project.MaxParticipants = existingProject.MaxParticipants;

                _logger.LogInformation("Updating project with ID {Id}: {Title}", id, project.Title);
                var updatedProject = await _projectRepository.UpdateAsync(id, project);

                if (updatedProject == null)
                {
                    return NotFound($"Project with ID {id} not found");
                }

                _logger.LogInformation("Project updated successfully: {Id}", id);
                return Ok(updatedProject);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating project with ID {Id}: {Message}", id, ex.Message);
                return StatusCode(500, $"An error occurred while updating project with ID {id}: {ex.Message}");
            }
        }

        // DELETE: api/projects/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProject(int id)
        {
            try
            {
                var result = await _projectRepository.DeleteAsync(id);
                if (!result)
                {
                    return NotFound($"Project with ID {id} not found");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting project with ID {Id}", id);
                return StatusCode(500, $"An error occurred while deleting project with ID {id}");
            }
        }
    }
}
