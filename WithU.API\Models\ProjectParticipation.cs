using System.ComponentModel.DataAnnotations;

namespace WithU.API.Models
{
    public class ProjectParticipation
    {
        public int Id { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        [Required]
        public int ProjectId { get; set; }
        
        public string Status { get; set; } = "Registered"; // Registered, Confirmed, Attended, Cancelled
        
        public int? HoursLogged { get; set; }
        
        public string? Feedback { get; set; }
        
        public int? Rating { get; set; } // 1-5 star rating
        
        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? CancelledAt { get; set; }
        
        public DateTime? AttendedAt { get; set; }
        
        // Navigation properties (would be used with EF Core)
        // public User User { get; set; } = null!;
        // public Project Project { get; set; } = null!;
        
        // For our in-memory implementation, we'll include these properties
        public string UserFullName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public string ProjectTitle { get; set; } = string.Empty;
    }
}
