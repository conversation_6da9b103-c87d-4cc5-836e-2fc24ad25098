using Microsoft.AspNetCore.Mvc;
using WithU.API.Models;

namespace WithU.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ServicesController : ControllerBase
    {
        private readonly ILogger<ServicesController> _logger;

        public ServicesController(ILogger<ServicesController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public IActionResult GetServices()
        {
            var services = new List<Service>
            {
                new Service
                {
                    Id = 1,
                    Title = "Project Planning",
                    Description = "We help companies plan and organize community service projects.",
                    Icon = "https://cdn-icons-png.flaticon.com/512/3281/3281289.png"
                },
                new Service
                {
                    Id = 2,
                    Title = "Volunteer Coordination",
                    Description = "We coordinate volunteers and manage participation for maximum impact.",
                    Icon = "https://cdn-icons-png.flaticon.com/512/1534/1534938.png"
                },
                new Service
                {
                    Id = 3,
                    Title = "Impact Assessment",
                    Description = "We measure and report on the impact of community service initiatives.",
                    Icon = "https://cdn-icons-png.flaticon.com/512/2620/2620244.png"
                },
                new Service
                {
                    Id = 4,
                    Title = "Community Connections",
                    Description = "We connect companies with community organizations that need support.",
                    Icon = "https://cdn-icons-png.flaticon.com/512/745/745205.png"
                }
            };

            return Ok(new { services });
        }

        [HttpGet("{id}")]
        public IActionResult GetService(int id)
        {
            var services = new List<Service>
            {
                new Service
                {
                    Id = 1,
                    Title = "Project Planning",
                    Description = "We help companies plan and organize community service projects.",
                    Icon = "https://cdn-icons-png.flaticon.com/512/3281/3281289.png"
                },
                new Service
                {
                    Id = 2,
                    Title = "Volunteer Coordination",
                    Description = "We coordinate volunteers and manage participation for maximum impact.",
                    Icon = "https://cdn-icons-png.flaticon.com/512/1534/1534938.png"
                },
                new Service
                {
                    Id = 3,
                    Title = "Impact Assessment",
                    Description = "We measure and report on the impact of community service initiatives.",
                    Icon = "https://cdn-icons-png.flaticon.com/512/2620/2620244.png"
                },
                new Service
                {
                    Id = 4,
                    Title = "Community Connections",
                    Description = "We connect companies with community organizations that need support.",
                    Icon = "https://cdn-icons-png.flaticon.com/512/745/745205.png"
                }
            };

            var service = services.FirstOrDefault(s => s.Id == id);
            if (service == null)
            {
                return NotFound();
            }

            return Ok(service);
        }
    }
}
