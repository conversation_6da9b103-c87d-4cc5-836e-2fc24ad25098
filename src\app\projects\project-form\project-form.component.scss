// Variables (Light Dark theme)
$primary-color: #3b82f6; // Blue
$secondary-color: #4b5563; // Gray
$accent-color: #6b7280; // Medium gray
$text-color: #1f2937; // Dark gray
$light-text: #9ca3af; // Light gray
$background-color: #f3f4f6; // Very light gray
$light-background: #ffffff; // White
$dark-background: #111827; // Very dark gray
$border-radius: 4px; // Smaller border radius
$box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); // Subtle shadow
$error-color: #ef4444; // Red
$success-color: #10b981; // Green

.project-form-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: $light-background;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
}

.form-header {
  margin-bottom: 2rem;
  text-align: center;

  h2 {
    font-size: 1.8rem;
    color: $text-color;
    margin-bottom: 0.5rem;
  }

  .form-subtitle {
    color: $light-text;
    font-size: 1rem;
  }
}

.loading-indicator, .error-message, .success-message {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: $border-radius;
  text-align: center;
}

.loading-indicator {
  background-color: rgba($primary-color, 0.1);
  color: $primary-color;
}

.error-message {
  background-color: rgba($error-color, 0.1);
  color: $error-color;
}

.success-message {
  background-color: rgba($success-color, 0.1);
  color: $success-color;
}

.project-form {
  .form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba($secondary-color, 0.1);

    &:last-child {
      border-bottom: none;
    }

    .section-title {
      font-size: 1.2rem;
      color: $text-color;
      margin-bottom: 1.5rem;
      font-weight: 600;
    }
  }

  .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;

    .form-group {
      flex: 1;
    }
  }

  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: $text-color;
    }

    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid rgba($secondary-color, 0.2);
      border-radius: $border-radius;
      font-size: 1rem;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
      }

      &.is-invalid {
        border-color: $error-color;
      }
    }

    textarea.form-control {
      resize: vertical;
      min-height: 100px;
    }

    .error-feedback {
      color: $error-color;
      font-size: 0.875rem;
      margin-top: 0.5rem;
    }

    .form-text {
      font-size: 0.875rem;
      color: $light-text;
      margin-top: 0.5rem;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;

    .btn {
      padding: 0.75rem 1.5rem;
      border-radius: $border-radius;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      font-size: 1rem;

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }

    .btn-primary {
      background-color: $primary-color;
      color: white;

      &:hover:not(:disabled) {
        background-color: darken($primary-color, 10%);
      }
    }

    .btn-secondary {
      background-color: $secondary-color;
      color: white;

      &:hover:not(:disabled) {
        background-color: darken($secondary-color, 10%);
      }
    }

    .btn-outline {
      background-color: transparent;
      border: 1px solid $secondary-color;
      color: $secondary-color;

      &:hover:not(:disabled) {
        background-color: rgba($secondary-color, 0.1);
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .project-form-container {
    padding: 1.5rem;
    margin: 1rem;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-actions {
    flex-direction: column-reverse;
    
    .btn {
      width: 100%;
      margin-bottom: 0.5rem;
    }
  }
}
