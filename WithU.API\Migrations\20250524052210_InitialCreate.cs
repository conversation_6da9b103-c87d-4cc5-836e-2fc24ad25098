﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WithU.API.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "ProjectParticipations",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "AttendedAt", "JoinedAt" },
                values: new object[] { new DateTime(2025, 5, 19, 5, 22, 8, 899, DateTimeKind.Utc).AddTicks(3494), new DateTime(2025, 5, 9, 5, 22, 8, 899, DateTimeKind.Utc).AddTicks(2873) });

            migrationBuilder.UpdateData(
                table: "ProjectParticipations",
                keyColumn: "Id",
                keyValue: 2,
                column: "JoinedAt",
                value: new DateTime(2025, 5, 21, 5, 22, 8, 899, DateTimeKind.Utc).AddTicks(5314));

            migrationBuilder.UpdateData(
                table: "ProjectParticipations",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "AttendedAt", "JoinedAt" },
                values: new object[] { new DateTime(2025, 5, 19, 5, 22, 8, 899, DateTimeKind.Utc).AddTicks(5327), new DateTime(2025, 5, 4, 5, 22, 8, 899, DateTimeKind.Utc).AddTicks(5325) });

            migrationBuilder.UpdateData(
                table: "Projects",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 22, 8, 898, DateTimeKind.Utc).AddTicks(7883), new DateTime(2025, 5, 24, 5, 22, 8, 898, DateTimeKind.Utc).AddTicks(8315) });

            migrationBuilder.UpdateData(
                table: "Projects",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 22, 8, 898, DateTimeKind.Utc).AddTicks(8702), new DateTime(2025, 5, 24, 5, 22, 8, 898, DateTimeKind.Utc).AddTicks(8703) });

            migrationBuilder.UpdateData(
                table: "Projects",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 22, 8, 898, DateTimeKind.Utc).AddTicks(8713), new DateTime(2025, 5, 24, 5, 22, 8, 898, DateTimeKind.Utc).AddTicks(8714) });

            migrationBuilder.UpdateData(
                table: "Projects",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 22, 8, 898, DateTimeKind.Utc).AddTicks(8722), new DateTime(2025, 5, 24, 5, 22, 8, 898, DateTimeKind.Utc).AddTicks(8723) });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 22, 8, 896, DateTimeKind.Utc).AddTicks(5237), new DateTime(2025, 5, 24, 5, 22, 8, 896, DateTimeKind.Utc).AddTicks(5711) });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 22, 8, 896, DateTimeKind.Utc).AddTicks(6158), new DateTime(2025, 5, 24, 5, 22, 8, 896, DateTimeKind.Utc).AddTicks(6159) });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 22, 8, 896, DateTimeKind.Utc).AddTicks(6168), new DateTime(2025, 5, 24, 5, 22, 8, 896, DateTimeKind.Utc).AddTicks(6169) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "ProjectParticipations",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "AttendedAt", "JoinedAt" },
                values: new object[] { new DateTime(2025, 5, 19, 5, 21, 37, 262, DateTimeKind.Utc).AddTicks(1000), new DateTime(2025, 5, 9, 5, 21, 37, 262, DateTimeKind.Utc).AddTicks(344) });

            migrationBuilder.UpdateData(
                table: "ProjectParticipations",
                keyColumn: "Id",
                keyValue: 2,
                column: "JoinedAt",
                value: new DateTime(2025, 5, 21, 5, 21, 37, 262, DateTimeKind.Utc).AddTicks(2040));

            migrationBuilder.UpdateData(
                table: "ProjectParticipations",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "AttendedAt", "JoinedAt" },
                values: new object[] { new DateTime(2025, 5, 19, 5, 21, 37, 262, DateTimeKind.Utc).AddTicks(2046), new DateTime(2025, 5, 4, 5, 21, 37, 262, DateTimeKind.Utc).AddTicks(2045) });

            migrationBuilder.UpdateData(
                table: "Projects",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 21, 37, 261, DateTimeKind.Utc).AddTicks(6568), new DateTime(2025, 5, 24, 5, 21, 37, 261, DateTimeKind.Utc).AddTicks(6775) });

            migrationBuilder.UpdateData(
                table: "Projects",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 21, 37, 261, DateTimeKind.Utc).AddTicks(6996), new DateTime(2025, 5, 24, 5, 21, 37, 261, DateTimeKind.Utc).AddTicks(6996) });

            migrationBuilder.UpdateData(
                table: "Projects",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 21, 37, 261, DateTimeKind.Utc).AddTicks(7001), new DateTime(2025, 5, 24, 5, 21, 37, 261, DateTimeKind.Utc).AddTicks(7001) });

            migrationBuilder.UpdateData(
                table: "Projects",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 21, 37, 261, DateTimeKind.Utc).AddTicks(7005), new DateTime(2025, 5, 24, 5, 21, 37, 261, DateTimeKind.Utc).AddTicks(7006) });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 21, 37, 259, DateTimeKind.Utc).AddTicks(9652), new DateTime(2025, 5, 24, 5, 21, 37, 259, DateTimeKind.Utc).AddTicks(9984) });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 21, 37, 260, DateTimeKind.Utc).AddTicks(299), new DateTime(2025, 5, 24, 5, 21, 37, 260, DateTimeKind.Utc).AddTicks(299) });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 5, 24, 5, 21, 37, 260, DateTimeKind.Utc).AddTicks(304), new DateTime(2025, 5, 24, 5, 21, 37, 260, DateTimeKind.Utc).AddTicks(305) });
        }
    }
}
