import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { ApiService, Project, Service } from '../services/api.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
  projects: Project[] = [];
  services: Service[] = [];
  loading = false;
  error = '';

  constructor(private apiService: ApiService, private router: Router) {}

  ngOnInit(): void {
    this.loadProjects();
    this.loadServices();
  }

  loadProjects(): void {
    this.loading = true;
    this.apiService.getProjects()
      .subscribe({
        next: (response) => {
          this.projects = response.projects;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading projects:', error);
          this.error = 'Failed to load projects. Please try again later.';
          this.loading = false;
        }
      });
  }

  loadServices(): void {
    this.apiService.getServices()
      .subscribe({
        next: (response) => {
          this.services = response.services;
        },
        error: (error) => {
          console.error('Error loading services:', error);
        }
      });
  }

  onJoinProject(projectTitle: string): void {
    console.log(`User clicked to join project: ${projectTitle}`);
    // Navigate to sign-up page
    this.router.navigate(['/register']);
  }
}
