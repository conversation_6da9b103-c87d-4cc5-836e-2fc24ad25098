using WithU.API.Models;

namespace WithU.API.Repositories
{
    public class InMemoryProjectParticipationRepository : IProjectParticipationRepository
    {
        private readonly List<ProjectParticipation> _participations = new List<ProjectParticipation>
        {
            new ProjectParticipation
            {
                Id = 1,
                UserId = 1,
                ProjectId = 1,
                Status = "Confirmed",
                HoursLogged = 4,
                Feedback = "Great experience! Looking forward to the next event.",
                Rating = 5,
                JoinedAt = DateTime.UtcNow.AddDays(-15),
                AttendedAt = DateTime.UtcNow.AddDays(-5),
                UserFullName = "John Doe",
                UserEmail = "<EMAIL>",
                ProjectTitle = "Community Garden Initiative"
            },
            new ProjectParticipation
            {
                Id = 2,
                UserId = 1,
                ProjectId = 3,
                Status = "Registered",
                JoinedAt = DateTime.UtcNow.AddDays(-3),
                UserFullName = "John Doe",
                UserEmail = "<EMAIL>",
                ProjectTitle = "Food Bank Volunteers"
            },
            new ProjectParticipation
            {
                Id = 3,
                UserId = 2,
                ProjectId = 1,
                Status = "Confirmed",
                HoursLogged = 6,
                Feedback = "Well organized and impactful.",
                Rating = 4,
                JoinedAt = DateTime.UtcNow.AddDays(-20),
                AttendedAt = DateTime.UtcNow.AddDays(-5),
                UserFullName = "Jane Smith",
                UserEmail = "<EMAIL>",
                ProjectTitle = "Community Garden Initiative"
            },
            new ProjectParticipation
            {
                Id = 4,
                UserId = 2,
                ProjectId = 2,
                Status = "Cancelled",
                JoinedAt = DateTime.UtcNow.AddDays(-10),
                CancelledAt = DateTime.UtcNow.AddDays(-2),
                UserFullName = "Jane Smith",
                UserEmail = "<EMAIL>",
                ProjectTitle = "Youth Mentorship Program"
            }
        };

        private int _nextId = 5;
        private readonly IProjectRepository _projectRepository;
        private readonly IUserRepository _userRepository;

        public InMemoryProjectParticipationRepository(
            IProjectRepository projectRepository,
            IUserRepository userRepository)
        {
            _projectRepository = projectRepository;
            _userRepository = userRepository;
        }

        public Task<IEnumerable<ProjectParticipation>> GetAllAsync()
        {
            return Task.FromResult<IEnumerable<ProjectParticipation>>(_participations);
        }

        public Task<ProjectParticipation?> GetByIdAsync(int id)
        {
            var participation = _participations.FirstOrDefault(p => p.Id == id);
            return Task.FromResult(participation);
        }

        public Task<IEnumerable<ProjectParticipation>> GetByUserIdAsync(int userId)
        {
            var participations = _participations.Where(p => p.UserId == userId);
            return Task.FromResult(participations);
        }

        public Task<IEnumerable<ProjectParticipation>> GetByProjectIdAsync(int projectId)
        {
            var participations = _participations.Where(p => p.ProjectId == projectId);
            return Task.FromResult(participations);
        }

        public Task<ProjectParticipation?> GetByUserAndProjectIdAsync(int userId, int projectId)
        {
            var participation = _participations.FirstOrDefault(p => p.UserId == userId && p.ProjectId == projectId);
            return Task.FromResult(participation);
        }

        public async Task<ProjectParticipation> CreateAsync(ProjectParticipation participation)
        {
            // Get user and project details to populate the participation record
            var user = await _userRepository.GetByIdAsync(participation.UserId);
            var project = await _projectRepository.GetByIdAsync(participation.ProjectId);

            if (user != null && project != null)
            {
                participation.UserFullName = user.FullName;
                participation.UserEmail = user.Email;
                participation.ProjectTitle = project.Title;
            }

            participation.Id = _nextId++;
            participation.JoinedAt = DateTime.UtcNow;
            _participations.Add(participation);

            // Update the project's participant count
            if (project != null)
            {
                project.Participants = await GetParticipantCountForProjectAsync(project.Id);
            }

            return participation;
        }

        public async Task<ProjectParticipation?> UpdateAsync(int id, ProjectParticipation updatedParticipation)
        {
            var existingParticipation = _participations.FirstOrDefault(p => p.Id == id);
            if (existingParticipation == null)
            {
                return null;
            }

            // Update properties
            existingParticipation.Status = updatedParticipation.Status;
            existingParticipation.HoursLogged = updatedParticipation.HoursLogged;
            existingParticipation.Feedback = updatedParticipation.Feedback;
            existingParticipation.Rating = updatedParticipation.Rating;

            // Update timestamps based on status
            if (updatedParticipation.Status == "Cancelled" && existingParticipation.CancelledAt == null)
            {
                existingParticipation.CancelledAt = DateTime.UtcNow;
            }
            else if (updatedParticipation.Status == "Attended" && existingParticipation.AttendedAt == null)
            {
                existingParticipation.AttendedAt = DateTime.UtcNow;
            }

            // Update the project's participant count if status changed
            var project = await _projectRepository.GetByIdAsync(existingParticipation.ProjectId);
            if (project != null)
            {
                project.Participants = await GetParticipantCountForProjectAsync(project.Id);
            }

            return existingParticipation;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var participation = _participations.FirstOrDefault(p => p.Id == id);
            if (participation == null)
            {
                return false;
            }

            _participations.Remove(participation);

            // Update the project's participant count
            var project = await _projectRepository.GetByIdAsync(participation.ProjectId);
            if (project != null)
            {
                project.Participants = await GetParticipantCountForProjectAsync(project.Id);
            }

            return true;
        }

        public Task<int> GetParticipantCountForProjectAsync(int projectId)
        {
            // Count active participants (Registered or Confirmed, not Cancelled)
            var count = _participations.Count(p => 
                p.ProjectId == projectId && 
                (p.Status == "Registered" || p.Status == "Confirmed" || p.Status == "Attended"));
            
            return Task.FromResult(count);
        }
    }
}
