<div class="project-list-container">
  <div class="list-header">
    <div class="header-content">
      <h2>Community Service Projects</h2>
      <p class="list-subtitle">Browse and manage community service projects</p>
    </div>
    <div class="header-actions">
      <a routerLink="/projects/new" class="btn btn-primary">Create New Project</a>
    </div>
  </div>

  <div class="filter-section">
    <div class="search-box">
      <input 
        type="text" 
        placeholder="Search projects..." 
        (input)="onSearchChange($event)"
        class="search-input"
      >
    </div>
    
    <div class="filter-controls">
      <div class="filter-group">
        <label for="statusFilter">Status:</label>
        <select 
          id="statusFilter" 
          (change)="onStatusFilterChange($any($event.target).value)" 
          class="filter-select"
        >
          <option *ngFor="let status of statusOptions" [value]="status">
            {{ status === 'all' ? 'All Statuses' : status }}
          </option>
        </select>
      </div>
      
      <div class="filter-group">
        <label for="categoryFilter">Category:</label>
        <select 
          id="categoryFilter" 
          (change)="onCategoryFilterChange($any($event.target).value)" 
          class="filter-select"
        >
          <option *ngFor="let category of categoryOptions" [value]="category">
            {{ category === 'all' ? 'All Categories' : category }}
          </option>
        </select>
      </div>
    </div>
  </div>

  <div *ngIf="loading" class="loading-indicator">
    <p>Loading projects...</p>
  </div>

  <div *ngIf="error" class="error-message">
    <p>{{ error }}</p>
  </div>

  <div *ngIf="!loading && !error && filteredProjects.length === 0" class="empty-state">
    <div class="empty-icon">📋</div>
    <h3>No Projects Found</h3>
    <p>There are no projects matching your current filters.</p>
    <button (click)="statusFilter = 'all'; categoryFilter = 'all'; searchTerm = ''; applyFilters()" class="btn btn-secondary">Clear Filters</button>
  </div>

  <div *ngIf="!loading && !error && filteredProjects.length > 0" class="project-grid">
    <div *ngFor="let project of filteredProjects" class="project-card" [routerLink]="['/projects', project.id]">
      <div class="project-image" [style.background-image]="'url(' + project.image + ')'" style="background-size: cover; background-position: center;">
        <div class="project-category">{{ project.category }}</div>
        <div class="project-status" [ngClass]="getStatusClass(project.status)">{{ project.status }}</div>
      </div>
      
      <div class="project-content">
        <h3 class="project-title">{{ project.title }}</h3>
        <p class="project-description">{{ project.description | slice:0:120 }}{{ project.description.length > 120 ? '...' : '' }}</p>
        
        <div class="project-details">
          <div class="detail-item">
            <span class="detail-icon">📅</span>
            <span>{{ project.date }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-icon">📍</span>
            <span>{{ project.location }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-icon">👥</span>
            <span>{{ project.participants }} / {{ project.maxParticipants > 0 ? project.maxParticipants : '∞' }}</span>
          </div>
        </div>
        
        <div class="project-company" *ngIf="project.companyName">
          <span class="company-label">Organized by:</span>
          <span class="company-name">{{ project.companyName }}</span>
        </div>
        
        <div class="project-actions">
          <a [routerLink]="['/projects', project.id]" class="btn btn-outline">View Details</a>
          <a [routerLink]="['/projects', project.id, 'edit']" class="btn btn-secondary" (click)="$event.stopPropagation()">Edit</a>
          <button class="btn btn-danger" (click)="deleteProject(project.id, $event)">Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
