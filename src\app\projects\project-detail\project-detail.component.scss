// Variables (Light Dark theme)
$primary-color: #3b82f6; // Blue
$secondary-color: #4b5563; // Gray
$accent-color: #6b7280; // Medium gray
$text-color: #1f2937; // Dark gray
$light-text: #9ca3af; // Light gray
$background-color: #f3f4f6; // Very light gray
$light-background: #ffffff; // White
$dark-background: #111827; // Very dark gray
$border-radius: 4px; // Smaller border radius
$box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); // Subtle shadow
$error-color: #ef4444; // Red
$success-color: #10b981; // Green
$warning-color: #f59e0b; // Amber
$info-color: #3b82f6; // Blue

.project-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  width: 100%;
}

.loading-indicator, .error-message, .success-message {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: $border-radius;
  text-align: center;
}

.loading-indicator {
  background-color: rgba($primary-color, 0.1);
  color: $primary-color;
}

.error-message {
  background-color: rgba($error-color, 0.1);
  color: $error-color;
}

.success-message {
  background-color: rgba($success-color, 0.1);
  color: $success-color;
}

.project-detail {
  background-color: $light-background;
  border-radius: $border-radius;
  overflow: hidden;
  box-shadow: $box-shadow;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba($secondary-color, 0.1);

  .back-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: $text-color;
    font-size: 1rem;
    cursor: pointer;
    padding: 0.5rem 0;
    transition: color 0.3s ease;

    &:hover {
      color: $primary-color;
    }

    .back-icon {
      font-size: 1.25rem;
    }
  }

  .header-actions {
    display: flex;
    gap: 1rem;

    .btn {
      padding: 0.5rem 1rem;
      border-radius: $border-radius;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;

      &.btn-secondary {
        background-color: $secondary-color;
        color: white;

        &:hover {
          background-color: darken($secondary-color, 10%);
        }
      }

      &.btn-danger {
        background-color: $error-color;
        color: white;

        &:hover {
          background-color: darken($error-color, 10%);
        }
      }
    }
  }
}

.project-hero {
  position: relative;
  height: 400px;

  .project-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.7) 100%);
      z-index: 2;
    }
  }

  .project-header-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 2rem;
    z-index: 3;
    color: white;

    .project-meta {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;

      .project-category, .project-status {
        padding: 0.5rem 0.75rem;
        border-radius: $border-radius;
        font-size: 0.875rem;
        font-weight: 500;
      }

      .project-category {
        background-color: rgba($dark-background, 0.7);
      }

      .project-status {
        &.status-upcoming {
          background-color: rgba($info-color, 0.9);
        }

        &.status-active {
          background-color: rgba($success-color, 0.9);
        }

        &.status-completed {
          background-color: rgba($secondary-color, 0.9);
        }

        &.status-cancelled {
          background-color: rgba($error-color, 0.9);
        }
      }
    }

    .project-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .project-company {
      font-size: 1rem;

      .company-label {
        opacity: 0.8;
        margin-right: 0.5rem;
      }

      .company-name {
        font-weight: 600;
      }
    }
  }
}

.project-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  padding: 2rem;

  .project-main {
    .content-section {
      margin-bottom: 2.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 1.5rem;
        color: $text-color;
        margin-bottom: 1rem;
        font-weight: 600;
      }

      .project-description {
        color: $text-color;
        line-height: 1.6;
        font-size: 1rem;
      }

      .skills-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;

        .skill-tag {
          background-color: rgba($primary-color, 0.1);
          color: $primary-color;
          padding: 0.5rem 0.75rem;
          border-radius: $border-radius;
          font-size: 0.875rem;
          font-weight: 500;
        }
      }

      .participants-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;

        .participant-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem;
          background-color: rgba($background-color, 0.5);
          border-radius: $border-radius;

          .participant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .participant-info {
            flex: 1;

            .participant-name {
              font-size: 0.9rem;
              font-weight: 500;
              color: $text-color;
              margin-bottom: 0.25rem;
            }

            .participant-status {
              display: inline-block;
              font-size: 0.75rem;
              padding: 0.15rem 0.4rem;
              border-radius: $border-radius;

              &.status-registered {
                background-color: rgba($info-color, 0.1);
                color: $info-color;
              }

              &.status-confirmed {
                background-color: rgba($primary-color, 0.1);
                color: $primary-color;
              }

              &.status-attended {
                background-color: rgba($success-color, 0.1);
                color: $success-color;
              }

              &.status-cancelled {
                background-color: rgba($error-color, 0.1);
                color: $error-color;
              }
            }
          }
        }
      }

      .participation-info {
        display: flex;
        gap: 2rem;
        margin-bottom: 1.5rem;

        .participation-stat {
          text-align: center;

          .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: $primary-color;
            margin-bottom: 0.5rem;
          }

          .stat-label {
            font-size: 0.875rem;
            color: $light-text;
          }
        }
      }

      .participation-progress {
        margin-bottom: 1.5rem;

        .progress-bar {
          height: 10px;
          background-color: rgba($primary-color, 0.1);
          border-radius: 5px;
          overflow: hidden;
          margin-bottom: 0.5rem;

          .progress-fill {
            height: 100%;
            background-color: $primary-color;
            border-radius: 5px;
          }
        }

        .progress-text {
          font-size: 0.875rem;
          color: $light-text;
          text-align: right;
        }
      }

      .join-action {
        .btn-primary {
          background-color: $primary-color;
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: $border-radius;
          font-weight: 500;
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            background-color: darken-color($primary-color, 10%);
            transform: translateY(-2px);
          }

          &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
          }

          &.btn-large {
            font-size: 1.1rem;
            padding: 1rem 2rem;
          }
        }

        .auth-buttons {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-top: 1rem;

          .btn {
            width: 100%;
            text-align: center;
            font-size: 1rem;
            padding: 0.75rem 1.5rem;
            border-radius: $border-radius;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;

            &.btn-primary {
              background-color: $primary-color;
              color: white;

              &:hover {
                background-color: darken-color($primary-color, 10%);
                transform: translateY(-2px);
              }
            }

            &.btn-secondary {
              background-color: $secondary-color;
              color: white;

              &:hover {
                background-color: darken-color($secondary-color, 10%);
                transform: translateY(-2px);
              }
            }
          }

          @media (min-width: 768px) {
            flex-direction: row;

            .btn {
              width: auto;
            }
          }
        }
      }
    }
  }

  .project-sidebar {
    .sidebar-section {
      background-color: rgba($background-color, 0.5);
      border-radius: $border-radius;
      padding: 1.5rem;
      margin-bottom: 1.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .sidebar-title {
        font-size: 1.25rem;
        color: $text-color;
        margin-bottom: 1.25rem;
        font-weight: 600;
      }

      .detail-item {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.25rem;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-icon {
          font-size: 1.5rem;
          color: $primary-color;
        }

        .detail-content {
          flex: 1;

          .detail-label {
            font-size: 0.875rem;
            color: $light-text;
            margin-bottom: 0.25rem;
          }

          .detail-value {
            font-size: 1rem;
            color: $text-color;
            font-weight: 500;

            a {
              color: $primary-color;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }

      .share-buttons {
        display: flex;
        gap: 0.75rem;

        .share-button {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          cursor: pointer;
          transition: transform 0.3s ease;

          &:hover {
            transform: translateY(-3px);
          }

          .share-icon {
            width: 20px;
            height: 20px;
          }

          &.facebook {
            background-color: #1877f2;
          }

          &.twitter {
            background-color: #1da1f2;
          }

          &.linkedin {
            background-color: #0a66c2;
          }

          &.email {
            background-color: $secondary-color;
          }
        }
      }
    }
  }
}

// Responsive styles
@media (max-width: 992px) {
  .project-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    .header-actions {
      width: 100%;
      justify-content: space-between;
    }
  }

  .project-hero {
    height: 300px;

    .project-header-content {
      .project-title {
        font-size: 1.8rem;
      }
    }
  }

  .participation-info {
    flex-direction: column;
    gap: 1rem !important;

    .participation-stat {
      text-align: left !important;
      display: flex;
      align-items: center;
      gap: 1rem;

      .stat-value {
        margin-bottom: 0 !important;
      }
    }
  }
}
