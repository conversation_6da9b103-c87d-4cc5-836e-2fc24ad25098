﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace WithU.API.Migrations
{
    /// <inheritdoc />
    public partial class InitialPostgreSQLMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ProjectParticipations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    ProjectId = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    HoursLogged = table.Column<int>(type: "integer", nullable: true),
                    Feedback = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Rating = table.Column<int>(type: "integer", nullable: true),
                    JoinedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CancelledAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AttendedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UserFullName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UserEmail = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ProjectTitle = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProjectParticipations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Projects",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Image = table.Column<string>(type: "text", nullable: false),
                    Participants = table.Column<int>(type: "integer", nullable: false),
                    Location = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Date = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Category = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CompanyName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    SkillsRequired = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    MaxParticipants = table.Column<int>(type: "integer", nullable: false),
                    ContactEmail = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Projects", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    FirstName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    LastName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Password = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    ProfileImage = table.Column<string>(type: "text", nullable: true),
                    Bio = table.Column<string>(type: "text", nullable: true),
                    Skills = table.Column<string>(type: "text", nullable: true),
                    CompanyName = table.Column<string>(type: "text", nullable: true),
                    JobTitle = table.Column<string>(type: "text", nullable: true),
                    Role = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "ProjectParticipations",
                columns: new[] { "Id", "AttendedAt", "CancelledAt", "Feedback", "HoursLogged", "JoinedAt", "ProjectId", "ProjectTitle", "Rating", "Status", "UserEmail", "UserFullName", "UserId" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 5, 19, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(7340), null, "Great experience! Looking forward to the next event.", 4, new DateTime(2025, 5, 9, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(7079), 1, "Community Garden Initiative", 5, "Confirmed", "<EMAIL>", "John Doe", 1 },
                    { 2, null, null, null, null, new DateTime(2025, 5, 21, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(8069), 3, "Food Bank Volunteers", null, "Registered", "<EMAIL>", "John Doe", 1 },
                    { 3, new DateTime(2025, 5, 19, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(8074), null, "Well organized and impactful.", 6, new DateTime(2025, 5, 4, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(8073), 1, "Community Garden Initiative", 4, "Confirmed", "<EMAIL>", "Jane Smith", 2 }
                });

            migrationBuilder.InsertData(
                table: "Projects",
                columns: new[] { "Id", "Category", "CompanyName", "ContactEmail", "ContactPhone", "CreatedAt", "Date", "Description", "Image", "Location", "MaxParticipants", "Participants", "SkillsRequired", "Status", "Title", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, "Environment", "Green Earth Inc.", "<EMAIL>", "(*************", new DateTime(2025, 5, 24, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(4530), "2023-12-15", "Help create and maintain community gardens in underserved neighborhoods.", "https://images.unsplash.com/photo-1464226184884-fa280b87c399", "Various Locations", 50, 2, "Gardening, Basic Construction", "Active", "Community Garden Initiative", new DateTime(2025, 5, 24, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(4918) },
                    { 2, "Education", "Future Leaders Foundation", "<EMAIL>", "(*************", new DateTime(2025, 5, 24, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(5203), "2023-12-20", "Connect with local youth to provide guidance, support, and positive role modeling.", "https://images.unsplash.com/photo-1529390079861-591de354faf5", "Community Center", 30, 0, "Communication, Patience, Mentoring", "Upcoming", "Youth Mentorship Program", new DateTime(2025, 5, 24, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(5204) },
                    { 3, "Community", "Nourish Together", "<EMAIL>", "(555) 345-6789", new DateTime(2025, 5, 24, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(5208), "2023-12-10", "Help sort, package, and distribute food to families in need.", "https://images.unsplash.com/photo-*************-ea4288922497", "Downtown Food Bank", 40, 1, "Organization, Teamwork", "Active", "Food Bank Volunteers", new DateTime(2025, 5, 24, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(5208) },
                    { 4, "Environment", "Clean City Initiative", "<EMAIL>", "(555) 456-7890", new DateTime(2025, 5, 24, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(5211), "2023-12-05", "Join us in cleaning up local parks and streets to create a cleaner community.", "https://images.unsplash.com/photo-*************-cf6ed80faba5", "City Park", 100, 0, "None", "Upcoming", "Neighborhood Cleanup", new DateTime(2025, 5, 24, 5, 38, 8, 321, DateTimeKind.Utc).AddTicks(5212) }
                });

            migrationBuilder.InsertData(
                table: "Users",
                columns: new[] { "Id", "Bio", "CompanyName", "CreatedAt", "Email", "FirstName", "JobTitle", "LastName", "Password", "PhoneNumber", "ProfileImage", "Role", "Skills", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, "Passionate about community service and making a difference.", "Tech Innovations Inc.", new DateTime(2025, 5, 24, 5, 38, 8, 320, DateTimeKind.Utc).AddTicks(6228), "<EMAIL>", "John", "Senior Developer", "Doe", "password123", "(*************", "https://randomuser.me/api/portraits/men/1.jpg", "Employee", "Leadership, Communication, Project Management", new DateTime(2025, 5, 24, 5, 38, 8, 320, DateTimeKind.Utc).AddTicks(6397) },
                    { 2, "Dedicated to helping others and building stronger communities.", "Green Earth Inc.", new DateTime(2025, 5, 24, 5, 38, 8, 320, DateTimeKind.Utc).AddTicks(6625), "<EMAIL>", "Jane", "Community Outreach Manager", "Smith", "password123", "(*************", "https://randomuser.me/api/portraits/women/1.jpg", "CompanyAdmin", "Teaching, Mentoring, Organization", new DateTime(2025, 5, 24, 5, 38, 8, 320, DateTimeKind.Utc).AddTicks(6626) },
                    { 3, "System administrator for the WithU platform.", "WithU", new DateTime(2025, 5, 24, 5, 38, 8, 320, DateTimeKind.Utc).AddTicks(6629), "<EMAIL>", "Admin", "System Administrator", "User", "admin123", "(555) 987-6543", "https://randomuser.me/api/portraits/men/10.jpg", "SystemAdmin", "Administration, Technical Support, User Management", new DateTime(2025, 5, 24, 5, 38, 8, 320, DateTimeKind.Utc).AddTicks(6630) }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Users_Email",
                table: "Users",
                column: "Email",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ProjectParticipations");

            migrationBuilder.DropTable(
                name: "Projects");

            migrationBuilder.DropTable(
                name: "Users");
        }
    }
}
