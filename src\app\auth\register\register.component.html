<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h2>Create Account</h2>
      <p>Join <PERSON><PERSON> to participate in community service projects.</p>
    </div>
    
    <div *ngIf="error" class="error-message">
      <p>{{ error }}</p>
    </div>
    
    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="auth-form">
      <div class="form-row">
        <div class="form-group">
          <label for="firstName">First Name *</label>
          <input 
            type="text" 
            id="firstName" 
            formControlName="firstName" 
            class="form-control" 
            [class.is-invalid]="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched"
          >
          <div *ngIf="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched" class="error-feedback">
            <span *ngIf="registerForm.get('firstName')?.errors?.['required']">First name is required.</span>
            <span *ngIf="registerForm.get('firstName')?.errors?.['minlength']">First name must be at least 2 characters.</span>
          </div>
        </div>
        
        <div class="form-group">
          <label for="lastName">Last Name *</label>
          <input 
            type="text" 
            id="lastName" 
            formControlName="lastName" 
            class="form-control" 
            [class.is-invalid]="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched"
          >
          <div *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched" class="error-feedback">
            <span *ngIf="registerForm.get('lastName')?.errors?.['required']">Last name is required.</span>
            <span *ngIf="registerForm.get('lastName')?.errors?.['minlength']">Last name must be at least 2 characters.</span>
          </div>
        </div>
      </div>
      
      <div class="form-group">
        <label for="email">Email *</label>
        <input 
          type="email" 
          id="email" 
          formControlName="email" 
          class="form-control" 
          [class.is-invalid]="registerForm.get('email')?.invalid && registerForm.get('email')?.touched"
          placeholder="<EMAIL>"
        >
        <div *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched" class="error-feedback">
          <span *ngIf="registerForm.get('email')?.errors?.['required']">Email is required.</span>
          <span *ngIf="registerForm.get('email')?.errors?.['email']">Please enter a valid email address.</span>
        </div>
      </div>
      
      <div class="form-group">
        <label for="password">Password *</label>
        <input 
          type="password" 
          id="password" 
          formControlName="password" 
          class="form-control" 
          [class.is-invalid]="registerForm.get('password')?.invalid && registerForm.get('password')?.touched"
        >
        <div *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched" class="error-feedback">
          <span *ngIf="registerForm.get('password')?.errors?.['required']">Password is required.</span>
          <span *ngIf="registerForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters.</span>
        </div>
      </div>
      
      <div class="form-group">
        <label for="phoneNumber">Phone Number</label>
        <input 
          type="tel" 
          id="phoneNumber" 
          formControlName="phoneNumber" 
          class="form-control" 
          placeholder="(*************"
        >
      </div>
      
      <div class="form-row">
        <div class="form-group">
          <label for="companyName">Company Name</label>
          <input 
            type="text" 
            id="companyName" 
            formControlName="companyName" 
            class="form-control"
          >
        </div>
        
        <div class="form-group">
          <label for="jobTitle">Job Title</label>
          <input 
            type="text" 
            id="jobTitle" 
            formControlName="jobTitle" 
            class="form-control"
          >
        </div>
      </div>
      
      <div class="form-actions">
        <button type="submit" class="btn btn-primary" [disabled]="loading">
          {{ loading ? 'Creating Account...' : 'Create Account' }}
        </button>
      </div>
    </form>
    
    <div class="auth-footer">
      <p>Already have an account? <a routerLink="/login">Sign In</a></p>
    </div>
  </div>
</div>
