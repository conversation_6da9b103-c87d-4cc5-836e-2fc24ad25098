# WithU (formerly Virtual Community Support)

A platform that allows companies to plan and manage efforts to motivate and enable employees to serve community needs through employer leadership.

## Features

- Showcase of community service projects
- Information about platform services
- Responsive design for all device types
- Simple contact form

## Prerequisites

- .NET 8 SDK (for the backend)
- Node.js (v14 or higher)
- Angular CLI (v19 or higher)
- PostgreSQL 12+ (for the database)

## Setup Instructions

### Option 1: Auto-Start (Recommended)

#### Windows
```bash
# Simply double-click the start-withu.bat file or run:
start-withu.bat
```

#### Linux/macOS
```bash
# Make the script executable (first time only)
chmod +x start-withu.sh

# Run the script
./start-withu.sh
```

This will:
1. Check for required prerequisites
2. Start the .NET API backend
3. Start the Angular frontend
4. Open your browser to the application

### Option 2: Docker Deployment

#### Windows
```bash
# Simply double-click the start-docker.bat file or run:
start-docker.bat
```

#### Linux/macOS
```bash
# Make the script executable (first time only)
chmod +x start-docker.sh

# Run the script
./start-docker.sh
```

This will:
1. Build and start Docker containers for both the API and frontend
2. Make the application available at http://localhost:4200

### Option 3: Manual Setup

#### 1. Set up PostgreSQL Database

**Install PostgreSQL:**
- Windows: Download from https://www.postgresql.org/download/windows/
- macOS: `brew install postgresql`
- Linux: `sudo apt-get install postgresql postgresql-contrib`

**Create databases:**
```sql
-- Connect as postgres user
psql -U postgres

-- Create databases
CREATE DATABASE withu_db;
CREATE DATABASE withu_dev_db;
```

**Update connection strings:**
Edit `WithU.API/appsettings.json` and `WithU.API/appsettings.Development.json` with your PostgreSQL password.

#### 2. Set up the .NET Backend

```bash
# Navigate to the API directory
cd WithU.API

# Restore packages
dotnet restore

# Apply database migrations
dotnet ef database update

# Run the API
dotnet run
```

The API will be available at:
- https://localhost:7080
- http://localhost:5080

#### 3. Set up the Angular Frontend

```bash
# From the root directory
npm install

# Start the Angular development server
ng serve
```

#### 4. Access the Application

Open your browser and navigate to `http://localhost:4200/`

## API Endpoints

### Projects

- `GET /api/projects` - Get all projects
- `GET /api/projects/{id}` - Get a specific project by ID

### Services

- `GET /api/services` - Get all services
- `GET /api/services/{id}` - Get a specific service by ID

## Technologies Used

- **Frontend**: Angular 17, TypeScript, SCSS
- **Backend**: ASP.NET Core 8, C#
- **Database**: PostgreSQL with Entity Framework Core
- **API**: RESTful API

## Project Structure

- `/src` - Angular frontend code
- `/WithU.API` - ASP.NET Core backend code
  - `/Controllers` - API controllers
  - `/Models` - Data models

## Angular CLI Commands

### Development server

```bash
ng serve
```

### Building for production

```bash
ng build
```

For more information on using the Angular CLI, visit the [Angular CLI Overview and Command Reference](https://angular.dev/tools/cli) page.

## .NET CLI Commands

### Running the API

```bash
cd WithU.API
dotnet run
```

### Building the API

```bash
cd WithU.API
dotnet build
```

For more information on using the .NET CLI, visit the [.NET CLI Overview](https://docs.microsoft.com/en-us/dotnet/core/tools/) page.
