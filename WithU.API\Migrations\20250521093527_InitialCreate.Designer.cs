﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WithU.API.Data;

#nullable disable

namespace WithU.API.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250521093527_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.13");

            modelBuilder.Entity("WithU.API.Models.Project", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Date")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Image")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("MaxParticipants")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Participants")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SkillsRequired")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Projects");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Category = "Environment",
                            CompanyName = "Green Earth Inc.",
                            ContactEmail = "<EMAIL>",
                            ContactPhone = "(*************",
                            CreatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1902),
                            Date = "2023-12-15",
                            Description = "Help create and maintain community gardens in underserved neighborhoods.",
                            Image = "https://images.unsplash.com/photo-1464226184884-fa280b87c399",
                            Location = "Various Locations",
                            MaxParticipants = 50,
                            Participants = 2,
                            SkillsRequired = "Gardening, Basic Construction",
                            Status = "Active",
                            Title = "Community Garden Initiative",
                            UpdatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1903)
                        },
                        new
                        {
                            Id = 2,
                            Category = "Education",
                            CompanyName = "Future Leaders Foundation",
                            ContactEmail = "<EMAIL>",
                            ContactPhone = "(*************",
                            CreatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1911),
                            Date = "2023-12-20",
                            Description = "Connect with local youth to provide guidance, support, and positive role modeling.",
                            Image = "https://images.unsplash.com/photo-1529390079861-591de354faf5",
                            Location = "Community Center",
                            MaxParticipants = 30,
                            Participants = 0,
                            SkillsRequired = "Communication, Patience, Mentoring",
                            Status = "Upcoming",
                            Title = "Youth Mentorship Program",
                            UpdatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1912)
                        },
                        new
                        {
                            Id = 3,
                            Category = "Community",
                            CompanyName = "Nourish Together",
                            ContactEmail = "<EMAIL>",
                            ContactPhone = "(*************",
                            CreatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1918),
                            Date = "2023-12-10",
                            Description = "Help sort, package, and distribute food to families in need.",
                            Image = "https://images.unsplash.com/photo-*************-ea4288922497",
                            Location = "Downtown Food Bank",
                            MaxParticipants = 40,
                            Participants = 1,
                            SkillsRequired = "Organization, Teamwork",
                            Status = "Active",
                            Title = "Food Bank Volunteers",
                            UpdatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1919)
                        },
                        new
                        {
                            Id = 4,
                            Category = "Environment",
                            CompanyName = "Clean City Initiative",
                            ContactEmail = "<EMAIL>",
                            ContactPhone = "(*************",
                            CreatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1924),
                            Date = "2023-12-05",
                            Description = "Join us in cleaning up local parks and streets to create a cleaner community.",
                            Image = "https://images.unsplash.com/photo-*************-cf6ed80faba5",
                            Location = "City Park",
                            MaxParticipants = 100,
                            Participants = 0,
                            SkillsRequired = "None",
                            Status = "Upcoming",
                            Title = "Neighborhood Cleanup",
                            UpdatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1925)
                        });
                });

            modelBuilder.Entity("WithU.API.Models.ProjectParticipation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("AttendedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CancelledAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Feedback")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int?>("HoursLogged")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("JoinedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProjectId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ProjectTitle")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("Rating")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserFullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("ProjectParticipations");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AttendedAt = new DateTime(2025, 5, 16, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1990),
                            Feedback = "Great experience! Looking forward to the next event.",
                            HoursLogged = 4,
                            JoinedAt = new DateTime(2025, 5, 6, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1984),
                            ProjectId = 1,
                            ProjectTitle = "Community Garden Initiative",
                            Rating = 5,
                            Status = "Confirmed",
                            UserEmail = "<EMAIL>",
                            UserFullName = "John Doe",
                            UserId = 1
                        },
                        new
                        {
                            Id = 2,
                            JoinedAt = new DateTime(2025, 5, 18, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1998),
                            ProjectId = 3,
                            ProjectTitle = "Food Bank Volunteers",
                            Status = "Registered",
                            UserEmail = "<EMAIL>",
                            UserFullName = "John Doe",
                            UserId = 1
                        },
                        new
                        {
                            Id = 3,
                            AttendedAt = new DateTime(2025, 5, 16, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(2004),
                            Feedback = "Well organized and impactful.",
                            HoursLogged = 6,
                            JoinedAt = new DateTime(2025, 5, 1, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(2003),
                            ProjectId = 1,
                            ProjectTitle = "Community Garden Initiative",
                            Rating = 4,
                            Status = "Confirmed",
                            UserEmail = "<EMAIL>",
                            UserFullName = "Jane Smith",
                            UserId = 2
                        });
                });

            modelBuilder.Entity("WithU.API.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Bio")
                        .HasColumnType("TEXT");

                    b.Property<string>("CompanyName")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("JobTitle")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProfileImage")
                        .HasColumnType("TEXT");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Skills")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Bio = "Passionate about community service and making a difference.",
                            CompanyName = "Tech Innovations Inc.",
                            CreatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1632),
                            Email = "<EMAIL>",
                            FirstName = "John",
                            JobTitle = "Senior Developer",
                            LastName = "Doe",
                            Password = "password123",
                            PhoneNumber = "(*************",
                            ProfileImage = "https://randomuser.me/api/portraits/men/1.jpg",
                            Role = "Employee",
                            Skills = "Leadership, Communication, Project Management",
                            UpdatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1633)
                        },
                        new
                        {
                            Id = 2,
                            Bio = "Dedicated to helping others and building stronger communities.",
                            CompanyName = "Green Earth Inc.",
                            CreatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1641),
                            Email = "<EMAIL>",
                            FirstName = "Jane",
                            JobTitle = "Community Outreach Manager",
                            LastName = "Smith",
                            Password = "password123",
                            PhoneNumber = "(*************",
                            ProfileImage = "https://randomuser.me/api/portraits/women/1.jpg",
                            Role = "CompanyAdmin",
                            Skills = "Teaching, Mentoring, Organization",
                            UpdatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1642)
                        },
                        new
                        {
                            Id = 3,
                            Bio = "System administrator for the WithU platform.",
                            CompanyName = "WithU",
                            CreatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1648),
                            Email = "<EMAIL>",
                            FirstName = "Admin",
                            JobTitle = "System Administrator",
                            LastName = "User",
                            Password = "admin123",
                            PhoneNumber = "(*************",
                            ProfileImage = "https://randomuser.me/api/portraits/men/10.jpg",
                            Role = "SystemAdmin",
                            Skills = "Administration, Technical Support, User Management",
                            UpdatedAt = new DateTime(2025, 5, 21, 9, 35, 24, 719, DateTimeKind.Utc).AddTicks(1648)
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
