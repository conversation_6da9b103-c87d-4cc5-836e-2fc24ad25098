using WithU.API.Models;

namespace WithU.API.Repositories
{
    public class InMemoryProjectRepository : IProjectRepository
    {
        private readonly List<Project> _projects = new List<Project>
        {
            new Project
            {
                Id = 1,
                Title = "Community Garden Initiative",
                Description = "Help create and maintain community gardens in underserved neighborhoods.",
                Image = "https://images.unsplash.com/photo-1464226184884-fa280b87c399",
                Participants = 24,
                Location = "Various Locations",
                Date = "2023-12-15",
                Status = "Active",
                Category = "Environment",
                CompanyName = "Green Earth Inc.",
                SkillsRequired = "Gardening, Basic Construction",
                MaxParticipants = 50,
                ContactEmail = "<EMAIL>",
                ContactPhone = "(*************",
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-5)
            },
            new Project
            {
                Id = 2,
                Title = "Youth Mentorship Program",
                Description = "Connect with local youth to provide guidance, support, and positive role modeling.",
                Image = "https://images.unsplash.com/photo-1529390079861-591de354faf5",
                Participants = 18,
                Location = "Community Center",
                Date = "2023-12-20",
                Status = "Upcoming",
                Category = "Education",
                CompanyName = "Future Leaders Foundation",
                SkillsRequired = "Communication, Patience, Mentoring",
                MaxParticipants = 30,
                ContactEmail = "<EMAIL>",
                ContactPhone = "(*************",
                CreatedAt = DateTime.UtcNow.AddDays(-20),
                UpdatedAt = DateTime.UtcNow.AddDays(-2)
            },
            new Project
            {
                Id = 3,
                Title = "Food Bank Volunteers",
                Description = "Help sort, package, and distribute food to families in need.",
                Image = "https://images.unsplash.com/photo-*************-ea4288922497",
                Participants = 32,
                Location = "Downtown Food Bank",
                Date = "2023-12-10",
                Status = "Active",
                Category = "Community",
                CompanyName = "Nourish Together",
                SkillsRequired = "Organization, Teamwork",
                MaxParticipants = 40,
                ContactEmail = "<EMAIL>",
                ContactPhone = "(*************",
                CreatedAt = DateTime.UtcNow.AddDays(-15),
                UpdatedAt = DateTime.UtcNow.AddDays(-1)
            },
            new Project
            {
                Id = 4,
                Title = "Neighborhood Cleanup",
                Description = "Join us in cleaning up local parks and streets to create a cleaner community.",
                Image = "https://images.unsplash.com/photo-*************-cf6ed80faba5",
                Participants = 15,
                Location = "City Park",
                Date = "2023-12-05",
                Status = "Upcoming",
                Category = "Environment",
                CompanyName = "Clean City Initiative",
                SkillsRequired = "None",
                MaxParticipants = 100,
                ContactEmail = "<EMAIL>",
                ContactPhone = "(*************",
                CreatedAt = DateTime.UtcNow.AddDays(-10),
                UpdatedAt = DateTime.UtcNow
            }
        };

        private int _nextId = 5;

        public Task<IEnumerable<Project>> GetAllAsync()
        {
            return Task.FromResult<IEnumerable<Project>>(_projects);
        }

        public Task<Project?> GetByIdAsync(int id)
        {
            var project = _projects.FirstOrDefault(p => p.Id == id);
            return Task.FromResult(project);
        }

        public Task<Project> CreateAsync(Project project)
        {
            project.Id = _nextId++;
            project.CreatedAt = DateTime.UtcNow;
            project.UpdatedAt = DateTime.UtcNow;
            _projects.Add(project);
            return Task.FromResult(project);
        }

        public Task<Project?> UpdateAsync(int id, Project updatedProject)
        {
            var existingProject = _projects.FirstOrDefault(p => p.Id == id);
            if (existingProject == null)
            {
                return Task.FromResult<Project?>(null);
            }

            // Update properties
            existingProject.Title = updatedProject.Title;
            existingProject.Description = updatedProject.Description;
            existingProject.Image = updatedProject.Image;
            existingProject.Location = updatedProject.Location;
            existingProject.Date = updatedProject.Date;
            existingProject.Status = updatedProject.Status;
            existingProject.Category = updatedProject.Category;
            existingProject.CompanyName = updatedProject.CompanyName;
            existingProject.SkillsRequired = updatedProject.SkillsRequired;
            existingProject.MaxParticipants = updatedProject.MaxParticipants;
            existingProject.ContactEmail = updatedProject.ContactEmail;
            existingProject.ContactPhone = updatedProject.ContactPhone;
            existingProject.UpdatedAt = DateTime.UtcNow;

            return Task.FromResult<Project?>(existingProject);
        }

        public Task<bool> DeleteAsync(int id)
        {
            var project = _projects.FirstOrDefault(p => p.Id == id);
            if (project == null)
            {
                return Task.FromResult(false);
            }

            _projects.Remove(project);
            return Task.FromResult(true);
        }
    }
}
