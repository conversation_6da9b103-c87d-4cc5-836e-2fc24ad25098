// Variables (Light Dark theme)
$primary-color: #3b82f6; // Blue
$secondary-color: #4b5563; // Gray
$accent-color: #6b7280; // Medium gray
$text-color: #1f2937; // Dark gray
$light-text: #9ca3af; // Light gray
$background-color: #f3f4f6; // Very light gray
$light-background: #ffffff; // White
$dark-background: #111827; // Very dark gray
$border-radius: 4px; // Smaller border radius
$box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); // Subtle shadow
$error-color: #ef4444; // Red
$success-color: #10b981; // Green
$warning-color: #f59e0b; // Amber
$info-color: #3b82f6; // Blue

.project-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  width: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  .header-content {
    h2 {
      font-size: 1.8rem;
      color: $text-color;
      margin-bottom: 0.5rem;
    }

    .list-subtitle {
      color: $light-text;
      font-size: 1rem;
    }
  }

  .header-actions {
    .btn-primary {
      background-color: $primary-color;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: $border-radius;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background-color: darken($primary-color, 10%);
        transform: translateY(-2px);
      }
    }
  }
}

.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: $light-background;
  border-radius: $border-radius;
  box-shadow: $box-shadow;

  .search-box {
    flex: 1;
    min-width: 250px;

    .search-input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid rgba($secondary-color, 0.2);
      border-radius: $border-radius;
      font-size: 1rem;

      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
      }
    }
  }

  .filter-controls {
    display: flex;
    gap: 1rem;

    .filter-group {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      label {
        font-weight: 500;
        color: $text-color;
      }

      .filter-select {
        padding: 0.75rem;
        border: 1px solid rgba($secondary-color, 0.2);
        border-radius: $border-radius;
        font-size: 1rem;
        background-color: white;
        min-width: 150px;

        &:focus {
          outline: none;
          border-color: $primary-color;
        }
      }
    }
  }
}

.loading-indicator, .error-message, .empty-state {
  padding: 2rem;
  margin-bottom: 1.5rem;
  border-radius: $border-radius;
  text-align: center;
  background-color: $light-background;
  box-shadow: $box-shadow;
}

.loading-indicator {
  color: $primary-color;
}

.error-message {
  color: $error-color;
}

.empty-state {
  padding: 3rem 2rem;

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: $light-text;
  }

  h3 {
    font-size: 1.5rem;
    color: $text-color;
    margin-bottom: 0.5rem;
  }

  p {
    color: $light-text;
    margin-bottom: 1.5rem;
  }

  .btn-secondary {
    background-color: $secondary-color;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: $border-radius;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken($secondary-color, 10%);
    }
  }
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.project-card {
  background-color: $light-background;
  border-radius: $border-radius;
  overflow: hidden;
  box-shadow: $box-shadow;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .project-image {
    height: 200px;
    position: relative;
    background-color: #e5e7eb;

    .project-category {
      position: absolute;
      top: 1rem;
      left: 1rem;
      background-color: rgba($dark-background, 0.7);
      color: white;
      padding: 0.5rem 0.75rem;
      border-radius: $border-radius;
      font-size: 0.75rem;
      font-weight: 500;
    }

    .project-status {
      position: absolute;
      top: 1rem;
      right: 1rem;
      padding: 0.5rem 0.75rem;
      border-radius: $border-radius;
      font-size: 0.75rem;
      font-weight: 500;

      &.status-upcoming {
        background-color: rgba($info-color, 0.9);
        color: white;
      }

      &.status-active {
        background-color: rgba($success-color, 0.9);
        color: white;
      }

      &.status-completed {
        background-color: rgba($secondary-color, 0.9);
        color: white;
      }

      &.status-cancelled {
        background-color: rgba($error-color, 0.9);
        color: white;
      }
    }
  }

  .project-content {
    padding: 1.5rem;

    .project-title {
      font-size: 1.25rem;
      color: $text-color;
      margin-bottom: 0.75rem;
      font-weight: 600;
    }

    .project-description {
      color: $light-text;
      margin-bottom: 1.25rem;
      font-size: 0.95rem;
      line-height: 1.5;
    }

    .project-details {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.25rem;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: $text-color;

        .detail-icon {
          font-size: 1rem;
        }
      }
    }

    .project-company {
      margin-bottom: 1.25rem;
      font-size: 0.875rem;

      .company-label {
        color: $light-text;
        margin-right: 0.5rem;
      }

      .company-name {
        color: $text-color;
        font-weight: 500;
      }
    }

    .project-actions {
      display: flex;
      gap: 0.75rem;

      .btn {
        padding: 0.5rem 1rem;
        border-radius: $border-radius;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        border: none;

        &.btn-outline {
          background-color: transparent;
          border: 1px solid $primary-color;
          color: $primary-color;

          &:hover {
            background-color: rgba($primary-color, 0.1);
          }
        }

        &.btn-secondary {
          background-color: $secondary-color;
          color: white;

          &:hover {
            background-color: darken($secondary-color, 10%);
          }
        }

        &.btn-danger {
          background-color: $error-color;
          color: white;

          &:hover {
            background-color: darken($error-color, 10%);
          }
        }
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    .header-actions {
      width: 100%;

      .btn-primary {
        display: block;
        width: 100%;
        text-align: center;
      }
    }
  }

  .filter-section {
    flex-direction: column;

    .filter-controls {
      width: 100%;
      flex-direction: column;
    }
  }

  .project-grid {
    grid-template-columns: 1fr;
  }
}
