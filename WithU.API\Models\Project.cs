using System.ComponentModel.DataAnnotations;

namespace WithU.API.Models
{
    public class Project
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [Url]
        public string Image { get; set; } = string.Empty;

        public int Participants { get; set; }

        [Required]
        public string Location { get; set; } = string.Empty;

        [Required]
        public string Date { get; set; } = string.Empty;

        [Required]
        public string Status { get; set; } = "Upcoming"; // Upcoming, Active, Completed, Cancelled

        public string Category { get; set; } = "Community"; // Community, Education, Environment, Health, etc.

        public string CompanyName { get; set; } = string.Empty;

        public string SkillsRequired { get; set; } = string.Empty;

        public int MaxParticipants { get; set; } = 0; // 0 means unlimited

        public string ContactEmail { get; set; } = string.Empty;

        public string ContactPhone { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
