@echo off
echo ===================================================
echo             STARTING WITHU APPLICATION
echo ===================================================
echo.

echo [1/4] Checking prerequisites...
where dotnet >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: .NET SDK not found. Please install .NET 8 SDK.
    echo Download from: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js not found. Please install Node.js.
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)

where ng >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Angular CLI not found. Will attempt to use npx.
    set USE_NPX=1
) else (
    set USE_NPX=0
)

echo [2/4] Starting .NET API...
start "WithU .NET API" cmd /c "cd WithU.API && dotnet run"
echo .NET API starting at http://localhost:5080 and https://localhost:7080
echo.

echo [3/4] Waiting for API to initialize (10 seconds)...
timeout /t 10 /nobreak >nul

echo [4/4] Starting Angular frontend...
if %USE_NPX% EQU 1 (
    start "WithU Angular Frontend" cmd /c "npx ng serve --open"
) else (
    start "WithU Angular Frontend" cmd /c "ng serve --open"
)

echo.
echo ===================================================
echo             WITHU APPLICATION STARTED
echo ===================================================
echo.
echo API: http://localhost:5080
echo Frontend: http://localhost:4200
echo.
echo Your browser should open automatically to http://localhost:4200
echo.
echo Press any key to stop all services and close this window...
pause >nul

echo Stopping services...
taskkill /FI "WINDOWTITLE eq WithU .NET API*" /F >nul 2>nul
taskkill /FI "WINDOWTITLE eq WithU Angular Frontend*" /F >nul 2>nul
echo Services stopped.
echo.
echo Thank you for using WithU!
timeout /t 3 >nul
