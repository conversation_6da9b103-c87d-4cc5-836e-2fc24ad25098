using WithU.API.Models;

namespace WithU.API.Repositories
{
    public class InMemoryUserRepository : IUserRepository
    {
        private readonly List<User> _users = new List<User>
        {
            new User
            {
                Id = 1,
                FirstName = "John",
                LastName = "Doe",
                Email = "<EMAIL>",
                Password = "password123", // In a real app, this would be hashed
                PhoneNumber = "(*************",
                ProfileImage = "https://randomuser.me/api/portraits/men/1.jpg",
                Bio = "Passionate about community service and making a difference.",
                Skills = "Leadership, Communication, Project Management",
                CompanyName = "Tech Innovations Inc.",
                JobTitle = "Senior Developer",
                Role = "Employee",
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-5)
            },
            new User
            {
                Id = 2,
                FirstName = "Jane",
                LastName = "Smith",
                Email = "<EMAIL>",
                Password = "password123", // In a real app, this would be hashed
                PhoneNumber = "(*************",
                ProfileImage = "https://randomuser.me/api/portraits/women/1.jpg",
                Bio = "Dedicated to helping others and building stronger communities.",
                Skills = "Teaching, Mentoring, Organization",
                CompanyName = "Green Earth Inc.",
                JobTitle = "Community Outreach Manager",
                Role = "CompanyAdmin",
                CreatedAt = DateTime.UtcNow.AddDays(-25),
                UpdatedAt = DateTime.UtcNow.AddDays(-3)
            },
            new User
            {
                Id = 3,
                FirstName = "Admin",
                LastName = "User",
                Email = "<EMAIL>",
                Password = "admin123", // In a real app, this would be hashed
                PhoneNumber = "(*************",
                ProfileImage = "https://randomuser.me/api/portraits/men/10.jpg",
                Bio = "System administrator for the WithU platform.",
                Skills = "Administration, Technical Support, User Management",
                CompanyName = "WithU",
                JobTitle = "System Administrator",
                Role = "SystemAdmin",
                CreatedAt = DateTime.UtcNow.AddDays(-60),
                UpdatedAt = DateTime.UtcNow.AddDays(-1)
            }
        };

        private int _nextId = 4;

        public Task<IEnumerable<User>> GetAllAsync()
        {
            return Task.FromResult<IEnumerable<User>>(_users);
        }

        public Task<User?> GetByIdAsync(int id)
        {
            var user = _users.FirstOrDefault(u => u.Id == id);
            return Task.FromResult(user);
        }

        public Task<User?> GetByEmailAsync(string email)
        {
            var user = _users.FirstOrDefault(u => u.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
            return Task.FromResult(user);
        }

        public Task<User> CreateAsync(User user)
        {
            user.Id = _nextId++;
            user.CreatedAt = DateTime.UtcNow;
            user.UpdatedAt = DateTime.UtcNow;
            _users.Add(user);
            return Task.FromResult(user);
        }

        public Task<User?> UpdateAsync(int id, User updatedUser)
        {
            var existingUser = _users.FirstOrDefault(u => u.Id == id);
            if (existingUser == null)
            {
                return Task.FromResult<User?>(null);
            }

            // Update properties
            existingUser.FirstName = updatedUser.FirstName;
            existingUser.LastName = updatedUser.LastName;
            existingUser.Email = updatedUser.Email;
            existingUser.PhoneNumber = updatedUser.PhoneNumber;
            existingUser.ProfileImage = updatedUser.ProfileImage;
            existingUser.Bio = updatedUser.Bio;
            existingUser.Skills = updatedUser.Skills;
            existingUser.CompanyName = updatedUser.CompanyName;
            existingUser.JobTitle = updatedUser.JobTitle;
            existingUser.UpdatedAt = DateTime.UtcNow;

            // Only update password if provided
            if (!string.IsNullOrEmpty(updatedUser.Password))
            {
                existingUser.Password = updatedUser.Password;
            }

            return Task.FromResult<User?>(existingUser);
        }

        public Task<bool> DeleteAsync(int id)
        {
            var user = _users.FirstOrDefault(u => u.Id == id);
            if (user == null)
            {
                return Task.FromResult(false);
            }

            _users.Remove(user);
            return Task.FromResult(true);
        }

        public Task<bool> ValidateCredentialsAsync(string email, string password)
        {
            // In a real app, we would hash the password and compare the hashes
            var user = _users.FirstOrDefault(u => 
                u.Email.Equals(email, StringComparison.OrdinalIgnoreCase) && 
                u.Password == password);
            
            return Task.FromResult(user != null);
        }
    }
}
