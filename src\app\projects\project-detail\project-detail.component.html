<div class="project-detail-container">
  <div *ngIf="loading" class="loading-indicator">
    <p>Loading project details...</p>
  </div>

  <div *ngIf="error" class="error-message">
    <p>{{ error }}</p>
  </div>

  <div *ngIf="!loading && !error && project" class="project-detail">
    <div class="detail-header">
      <button (click)="goBack()" class="back-button">
        <span class="back-icon">←</span> Back to Projects
      </button>

      <div class="header-actions">
        <button (click)="editProject()" class="btn btn-secondary">Edit Project</button>
        <button (click)="deleteProject()" class="btn btn-danger">Delete Project</button>
      </div>
    </div>

    <div class="project-hero">
      <div class="project-image" [style.background-image]="'url(' + project.image + ')'" style="background-size: cover; background-position: center;">
        <div class="image-overlay"></div>
      </div>

      <div class="project-header-content">
        <div class="project-meta">
          <div class="project-category">{{ project.category }}</div>
          <div class="project-status" [ngClass]="getStatusClass(project.status)">{{ project.status }}</div>
        </div>

        <h1 class="project-title">{{ project.title }}</h1>

        <div class="project-company" *ngIf="project.companyName">
          <span class="company-label">Organized by:</span>
          <span class="company-name">{{ project.companyName }}</span>
        </div>
      </div>
    </div>

    <div class="project-content">
      <div class="project-main">
        <section class="content-section">
          <h2 class="section-title">About This Project</h2>
          <p class="project-description">{{ project.description }}</p>
        </section>

        <section class="content-section" *ngIf="project.skillsRequired">
          <h2 class="section-title">Skills Required</h2>
          <div class="skills-list">
            <span class="skill-tag" *ngFor="let skill of project.skillsRequired.split(',')">{{ skill.trim() }}</span>
          </div>
        </section>

        <section class="content-section" *ngIf="apiService.isLoggedIn() && projectParticipations.length > 0">
          <h2 class="section-title">Project Participants</h2>

          <div *ngIf="loadingParticipations" class="loading-indicator">
            <p>Loading participants...</p>
          </div>

          <div class="participants-list">
            <div *ngFor="let participation of projectParticipations" class="participant-item">
              <div class="participant-avatar">
                <img src="https://ui-avatars.com/api/?name={{ participation.userFullName.split(' ').join('+') }}&background=3b82f6&color=fff" [alt]="participation.userFullName">
              </div>
              <div class="participant-info">
                <div class="participant-name">{{ participation.userFullName }}</div>
                <div class="participant-status" [ngClass]="getParticipationStatusClass(participation.status)">{{ participation.status }}</div>
              </div>
            </div>
          </div>
        </section>

        <section class="content-section">
          <h2 class="section-title">Participation</h2>
          <div class="participation-info">
            <div class="participation-stat">
              <div class="stat-value">{{ project.participants }}</div>
              <div class="stat-label">Current Participants</div>
            </div>

            <div class="participation-stat" *ngIf="project.maxParticipants > 0">
              <div class="stat-value">{{ project.maxParticipants }}</div>
              <div class="stat-label">Maximum Participants</div>
            </div>

            <div class="participation-stat" *ngIf="project.maxParticipants > 0">
              <div class="stat-value">{{ project.maxParticipants - project.participants }}</div>
              <div class="stat-label">Spots Remaining</div>
            </div>
          </div>

          <div class="participation-progress" *ngIf="project.maxParticipants > 0">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="(project.participants / project.maxParticipants) * 100"></div>
            </div>
            <div class="progress-text">
              {{ (project.participants / project.maxParticipants) * 100 | number:'1.0-0' }}% Full
            </div>
          </div>

          <div *ngIf="participationError" class="error-message">
            <p>{{ participationError }}</p>
          </div>

          <div *ngIf="participationSuccess" class="success-message">
            <p>{{ participationSuccess }}</p>
          </div>

          <div class="join-action">
            <button *ngIf="canJoinProject()" (click)="joinProject()" class="btn btn-primary btn-large" [disabled]="joiningProject">
              {{ joiningProject ? 'Joining...' : 'Join This Project' }}
            </button>

            <button *ngIf="userParticipation && canLeaveProject()" (click)="leaveProject()" class="btn btn-outline btn-large" [disabled]="leavingProject">
              {{ leavingProject ? 'Leaving...' : 'Leave Project' }}
            </button>

            <button *ngIf="userParticipation && !canLeaveProject()" class="btn btn-primary btn-large" disabled>
              {{ getJoinButtonText() }}
            </button>

            <button *ngIf="!canJoinProject() && !userParticipation" class="btn btn-primary btn-large" disabled>
              {{ getJoinButtonText() }}
            </button>

            <div *ngIf="!currentUser" class="auth-buttons">
              <a routerLink="/login" [queryParams]="{returnUrl: '/projects/' + project.id}" class="btn btn-primary">
                Login to Join
              </a>
              <a routerLink="/register" [queryParams]="{returnUrl: '/projects/' + project.id}" class="btn btn-secondary">
                Sign Up to Join
              </a>
            </div>
          </div>
        </section>
      </div>

      <div class="project-sidebar">
        <div class="sidebar-section">
          <h3 class="sidebar-title">Project Details</h3>

          <div class="detail-item">
            <div class="detail-icon">📅</div>
            <div class="detail-content">
              <div class="detail-label">Date</div>
              <div class="detail-value">{{ project.date }}</div>
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-icon">📍</div>
            <div class="detail-content">
              <div class="detail-label">Location</div>
              <div class="detail-value">{{ project.location }}</div>
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-icon">👥</div>
            <div class="detail-content">
              <div class="detail-label">Participants</div>
              <div class="detail-value">{{ project.participants }} {{ project.maxParticipants > 0 ? '/ ' + project.maxParticipants : '' }}</div>
            </div>
          </div>
        </div>

        <div class="sidebar-section" *ngIf="project.contactEmail || project.contactPhone">
          <h3 class="sidebar-title">Contact Information</h3>

          <div class="detail-item" *ngIf="project.contactEmail">
            <div class="detail-icon">✉️</div>
            <div class="detail-content">
              <div class="detail-label">Email</div>
              <div class="detail-value">
                <a [href]="'mailto:' + project.contactEmail">{{ project.contactEmail }}</a>
              </div>
            </div>
          </div>

          <div class="detail-item" *ngIf="project.contactPhone">
            <div class="detail-icon">📞</div>
            <div class="detail-content">
              <div class="detail-label">Phone</div>
              <div class="detail-value">
                <a [href]="'tel:' + project.contactPhone">{{ project.contactPhone }}</a>
              </div>
            </div>
          </div>
        </div>

        <div class="sidebar-section">
          <h3 class="sidebar-title">Share This Project</h3>

          <div class="share-buttons">
            <button class="share-button facebook">
              <img src="https://cdn-icons-png.flaticon.com/512/733/733547.png" alt="Facebook" class="share-icon">
            </button>
            <button class="share-button twitter">
              <img src="https://cdn-icons-png.flaticon.com/512/3670/3670151.png" alt="Twitter" class="share-icon">
            </button>
            <button class="share-button linkedin">
              <img src="https://cdn-icons-png.flaticon.com/512/3536/3536505.png" alt="LinkedIn" class="share-icon">
            </button>
            <button class="share-button email">
              <img src="https://cdn-icons-png.flaticon.com/512/3059/3059502.png" alt="Email" class="share-icon">
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
