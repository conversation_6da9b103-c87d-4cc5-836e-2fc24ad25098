# WithU .NET API

This is the backend API for the WithU platform, built with ASP.NET Core.

## Prerequisites

- [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)

## Running the API

### Using the Command Line

1. Navigate to the API directory:
   ```
   cd WithU.API
   ```

2. Run the API:
   ```
   dotnet run
   ```

3. The API will be available at:
   - https://localhost:7080
   - http://localhost:5080

### Using the Batch File

1. Simply double-click the `start-dotnet-api.bat` file in the root directory.

## API Endpoints

### Projects

- `GET /api/projects` - Get all projects
- `GET /api/projects/{id}` - Get a specific project by ID

### Services

- `GET /api/services` - Get all services
- `GET /api/services/{id}` - Get a specific service by ID

## Running the Full Application

1. Start the .NET API using one of the methods above.
2. In a separate terminal, start the Angular frontend:
   ```
   ng serve
   ```
3. Access the application at http://localhost:4200
