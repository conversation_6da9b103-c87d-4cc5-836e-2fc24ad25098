import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ApiService, User, ProjectParticipation } from '../../services/api.service';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss'
})
export class ProfileComponent implements OnInit {
  user: User | null = null;
  participations: ProjectParticipation[] = [];
  profileForm!: FormGroup;

  loading = false;
  loadingParticipations = false;
  error = '';
  success = '';

  editMode = false;

  constructor(
    private apiService: ApiService,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadUserData();
  }

  initForm(): void {
    this.profileForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],
      profileImage: ['', [Validators.pattern('https?://.+')]],
      bio: [''],
      skills: [''],
      companyName: [''],
      jobTitle: ['']
    });

    // Disable form initially
    this.profileForm.disable();
  }

  loadUserData(): void {
    this.loading = true;

    this.apiService.currentUser.subscribe({
      next: (user) => {
        this.user = user;

        if (user) {
          // Populate form with user data
          this.profileForm.patchValue({
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            profileImage: user.profileImage || '',
            companyName: user.companyName || '',
            jobTitle: user.jobTitle || ''
          });

          // Load user participations
          this.loadUserParticipations(user.id);
        }

        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load user data';
        this.loading = false;
        console.error('Error loading user data:', err);
      }
    });
  }

  loadUserParticipations(userId: number): void {
    this.loadingParticipations = true;

    this.apiService.getUserParticipations(userId).subscribe({
      next: (response) => {
        this.participations = response.participations;
        this.loadingParticipations = false;
      },
      error: (err) => {
        this.error = 'Failed to load participation data';
        this.loadingParticipations = false;
        console.error('Error loading participations:', err);
      }
    });
  }

  toggleEditMode(): void {
    this.editMode = !this.editMode;

    if (this.editMode) {
      this.profileForm.enable();
      // Keep email disabled as it's typically not changed
      this.profileForm.get('email')?.disable();
    } else {
      this.profileForm.disable();
    }
  }

  onSubmit(): void {
    if (this.profileForm.invalid || !this.user) {
      this.markFormGroupTouched(this.profileForm);
      return;
    }

    this.loading = true;
    this.error = '';
    this.success = '';

    // Get form values, but re-enable email field temporarily to get its value
    const emailControl = this.profileForm.get('email');
    const emailDisabled = emailControl?.disabled;
    if (emailDisabled) emailControl?.enable();
    const formValues = this.profileForm.value;
    if (emailDisabled) emailControl?.disable();

    // Create a complete user object with all required fields
    const updatedUser = {
      ...this.user,
      ...formValues,
      // Make sure we don't lose any fields from the original user
      id: this.user.id,
      role: this.user.role,
      email: formValues.email || this.user.email,
      firstName: formValues.firstName || this.user.firstName,
      lastName: formValues.lastName || this.user.lastName,
      // Include these fields even if they're null/undefined
      profileImage: formValues.profileImage,
      companyName: formValues.companyName,
      jobTitle: formValues.jobTitle,
      phoneNumber: formValues.phoneNumber,
      bio: formValues.bio,
      skills: formValues.skills
    };

    console.log('Updating user with data:', updatedUser);

    this.apiService.updateUser(this.user.id, updatedUser).subscribe({
      next: (user) => {
        this.success = 'Profile updated successfully';
        this.loading = false;
        this.editMode = false;
        this.profileForm.disable();

        // Update the current user in the service
        if (user) {
          // Create a new user object that matches the User interface
          const updatedCurrentUser: User = {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            role: user.role,
            profileImage: user.profileImage,
            companyName: user.companyName,
            jobTitle: user.jobTitle,
            phoneNumber: user.phoneNumber,
            bio: user.bio,
            skills: user.skills
          };

          // Update the current user in the service
          this.apiService.updateCurrentUser(updatedCurrentUser);

          // Update the local user object
          this.user = updatedCurrentUser;

          // Refresh the form with the updated user data
          this.initForm();
        }
      },
      error: (err) => {
        this.error = 'Failed to update profile: ' + (err.error?.message || err.message || 'Unknown error');
        this.loading = false;
        console.error('Error updating profile:', err);
      }
    });
  }

  // Helper method to mark all form controls as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  cancelEdit(): void {
    this.editMode = false;
    this.profileForm.disable();

    // Reset form to original values
    if (this.user) {
      this.profileForm.patchValue({
        firstName: this.user.firstName,
        lastName: this.user.lastName,
        email: this.user.email,
        profileImage: this.user.profileImage || '',
        companyName: this.user.companyName || '',
        jobTitle: this.user.jobTitle || ''
      });
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Registered':
        return 'status-registered';
      case 'Confirmed':
        return 'status-confirmed';
      case 'Attended':
        return 'status-attended';
      case 'Cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  logout(): void {
    this.apiService.logout();
    // Redirect to home page will be handled by the auth guard
  }
}
