using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Add DbContext
builder.Services.AddDbContext<WithU.API.Data.ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection"))
);

// Register repositories
builder.Services.AddScoped<WithU.API.Repositories.IProjectRepository, WithU.API.Repositories.EfProjectRepository>();
builder.Services.AddScoped<WithU.API.Repositories.IUserRepository, WithU.API.Repositories.EfUserRepository>();
builder.Services.AddScoped<WithU.API.Repositories.IProjectParticipationRepository, WithU.API.Repositories.EfProjectParticipationRepository>();

// Register services
builder.Services.AddScoped<WithU.API.Services.IAuthService, WithU.API.Services.SimpleAuthService>();

// Add CORS policy
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAngularApp",
        policy =>
        {
            policy.AllowAnyOrigin()
                  .AllowAnyHeader()
                  .AllowAnyMethod();
        });
});

var app = builder.Build();

// Initialize the database
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<WithU.API.Data.ApplicationDbContext>();

        // Apply any pending migrations
        if (context.Database.GetPendingMigrations().Any())
        {
            Console.WriteLine("Applying pending migrations...");
            context.Database.Migrate();
        }
        else
        {
            // If no migrations exist, create the database
            context.Database.EnsureCreated();
        }

        Console.WriteLine("Database initialized successfully.");
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while initializing the database.");
        Console.WriteLine($"Error initializing database: {ex.Message}");
    }
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

// Use CORS
app.UseCors("AllowAngularApp");

// Map controllers
app.MapControllers();

app.Run();
