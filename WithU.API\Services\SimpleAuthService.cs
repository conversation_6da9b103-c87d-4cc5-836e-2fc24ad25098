using System.Text;
using WithU.API.Models;
using WithU.API.Repositories;

namespace WithU.API.Services
{
    public class SimpleAuthService : IAuthService
    {
        private readonly IUserRepository _userRepository;
        private readonly Dictionary<string, int> _tokens = new Dictionary<string, int>();

        public SimpleAuthService(IUserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        public async Task<AuthResponse> LoginAsync(LoginRequest request)
        {
            var isValid = await _userRepository.ValidateCredentialsAsync(request.Email, request.Password);
            if (!isValid)
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "Invalid email or password"
                };
            }

            var user = await _userRepository.GetByEmailAsync(request.Email);
            if (user == null)
            {
                return new AuthResponse
                {
                    Success = false,
                    Message = "User not found"
                };
            }

            var token = GenerateToken(user);
            _tokens[token] = user.Id;

            return new AuthResponse
            {
                Success = true,
                Token = token,
                User = MapToUserDto(user)
            };
        }

        public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(request.Email))
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Email is required"
                    };
                }

                if (string.IsNullOrWhiteSpace(request.Password) || request.Password.Length < 6)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Password must be at least 6 characters"
                    };
                }

                if (string.IsNullOrWhiteSpace(request.FirstName) || request.FirstName.Length < 2)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "First name must be at least 2 characters"
                    };
                }

                if (string.IsNullOrWhiteSpace(request.LastName) || request.LastName.Length < 2)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Last name must be at least 2 characters"
                    };
                }

                // Check if email is already in use
                var existingUser = await _userRepository.GetByEmailAsync(request.Email);
                if (existingUser != null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Email already in use"
                    };
                }

                // Create new user
                var newUser = new User
                {
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    Password = request.Password, // In a real app, this would be hashed
                    PhoneNumber = request.PhoneNumber,
                    CompanyName = request.CompanyName,
                    JobTitle = request.JobTitle,
                    Role = "Employee", // Default role for new users
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                var createdUser = await _userRepository.CreateAsync(newUser);

                if (createdUser == null || createdUser.Id <= 0)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Failed to create user account"
                    };
                }

                var token = GenerateToken(createdUser);
                _tokens[token] = createdUser.Id;

                return new AuthResponse
                {
                    Success = true,
                    Token = token,
                    User = MapToUserDto(createdUser),
                    Message = "Registration successful"
                };
            }
            catch (Exception ex)
            {
                // Log the exception (in a real app)
                Console.WriteLine($"Error in RegisterAsync: {ex.Message}");

                return new AuthResponse
                {
                    Success = false,
                    Message = $"Registration failed: {ex.Message}"
                };
            }
        }

        public async Task<UserDto?> GetCurrentUserAsync(string token)
        {
            if (!_tokens.TryGetValue(token, out var userId))
            {
                return null;
            }

            var user = await _userRepository.GetByIdAsync(userId);
            return user != null ? MapToUserDto(user) : null;
        }

        private string GenerateToken(User user)
        {
            // In a real app, we would use JWT or another token system
            // This is a simple implementation for demonstration purposes
            var tokenData = $"{user.Id}:{user.Email}:{Guid.NewGuid()}:{DateTime.UtcNow.Ticks}";
            var tokenBytes = Encoding.UTF8.GetBytes(tokenData);
            return Convert.ToBase64String(tokenBytes);
        }

        private UserDto MapToUserDto(User user)
        {
            return new UserDto
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                ProfileImage = user.ProfileImage,
                CompanyName = user.CompanyName,
                JobTitle = user.JobTitle,
                Role = user.Role
            };
        }
    }
}
