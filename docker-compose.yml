version: '3.8'

services:
  api:
    build:
      context: ./WithU.API
      dockerfile: Dockerfile
    ports:
      - "5080:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    restart: unless-stopped
    container_name: withu-api

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.angular
    ports:
      - "4200:80"
    depends_on:
      - api
    restart: unless-stopped
    container_name: withu-frontend
