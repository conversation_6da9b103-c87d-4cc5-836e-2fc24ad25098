// Variables (Light Dark theme)
$primary-color: #3b82f6; // Blue
$secondary-color: #4b5563; // Gray
$accent-color: #6b7280; // Medium gray
$text-color: #1f2937; // Dark gray
$light-text: #9ca3af; // Light gray
$background-color: #f3f4f6; // Very light gray
$light-background: #ffffff; // White
$dark-background: #111827; // Very dark gray
$border-radius: 4px; // Smaller border radius
$box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); // Subtle shadow
$error-color: #ef4444; // Red

.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 200px);
  padding: 2rem 1rem;
  background-color: $background-color;
}

.auth-card {
  width: 100%;
  max-width: 450px;
  background-color: $light-background;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 2rem;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h2 {
    font-size: 1.8rem;
    color: $text-color;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: $light-text;
    font-size: 1rem;
  }
}

.error-message {
  background-color: rgba($error-color, 0.1);
  color: $error-color;
  padding: 1rem;
  border-radius: $border-radius;
  margin-bottom: 1.5rem;
  text-align: center;
  
  p {
    margin: 0;
  }
}

.auth-form {
  .form-group {
    margin-bottom: 1.5rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: $text-color;
    }
    
    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid rgba($secondary-color, 0.2);
      border-radius: $border-radius;
      font-size: 1rem;
      transition: border-color 0.3s ease;
      
      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
      }
      
      &.is-invalid {
        border-color: $error-color;
      }
    }
    
    .error-feedback {
      color: $error-color;
      font-size: 0.875rem;
      margin-top: 0.5rem;
    }
  }
  
  .form-actions {
    margin-top: 2rem;
    
    .btn {
      width: 100%;
      padding: 0.75rem;
      border-radius: $border-radius;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      font-size: 1rem;
      
      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }
    
    .btn-primary {
      background-color: $primary-color;
      color: white;
      
      &:hover:not(:disabled) {
        background-color: darken($primary-color, 10%);
      }
    }
  }
}

.auth-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba($secondary-color, 0.1);
  
  p {
    color: $light-text;
    font-size: 0.9rem;
    
    a {
      color: $primary-color;
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
