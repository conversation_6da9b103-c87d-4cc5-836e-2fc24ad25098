// Variables (Light Dark theme)
$primary-color: #3b82f6; // Blue
$secondary-color: #4b5563; // Gray
$accent-color: #6b7280; // Medium gray
$text-color: #1f2937; // Dark gray
$light-text: #9ca3af; // Light gray
$background-color: #f3f4f6; // Very light gray
$light-background: #ffffff; // White
$dark-background: #111827; // Very dark gray
$border-radius: 4px; // Smaller border radius
$box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); // Subtle shadow
$error-color: #ef4444; // Red
$success-color: #10b981; // Green
$warning-color: #f59e0b; // Amber
$info-color: #3b82f6; // Blue

.profile-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.loading-indicator, .error-message, .success-message, .empty-state {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: $border-radius;
  text-align: center;
}

.loading-indicator {
  background-color: rgba($primary-color, 0.1);
  color: $primary-color;
}

.error-message {
  background-color: rgba($error-color, 0.1);
  color: $error-color;
}

.success-message {
  background-color: rgba($success-color, 0.1);
  color: $success-color;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h2 {
    font-size: 1.8rem;
    color: $text-color;
    margin: 0;
  }
  
  .profile-actions {
    display: flex;
    gap: 1rem;
    
    .btn {
      padding: 0.5rem 1rem;
      border-radius: $border-radius;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      border: none;
      
      &.btn-secondary {
        background-color: $secondary-color;
        color: white;
        
        &:hover {
          background-color: darken($secondary-color, 10%);
        }
      }
      
      &.btn-outline {
        background-color: transparent;
        border: 1px solid $secondary-color;
        color: $secondary-color;
        
        &:hover {
          background-color: rgba($secondary-color, 0.1);
        }
      }
    }
  }
}

.profile-card {
  background-color: $light-background;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 1.5rem;
  margin-bottom: 2rem;
  
  .profile-info {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    
    .profile-avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .profile-details {
      flex: 1;
      
      h3 {
        font-size: 1.5rem;
        color: $text-color;
        margin: 0 0 0.5rem 0;
      }
      
      .profile-role {
        display: inline-block;
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
        padding: 0.25rem 0.5rem;
        border-radius: $border-radius;
        font-size: 0.75rem;
        font-weight: 500;
        margin-bottom: 0.75rem;
      }
      
      .profile-company {
        color: $text-color;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
      }
      
      .profile-email {
        color: $light-text;
        font-size: 0.9rem;
        margin: 0;
      }
    }
  }
}

.profile-form {
  .form-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba($secondary-color, 0.1);
    
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
    
    .section-title {
      font-size: 1.1rem;
      color: $text-color;
      margin-bottom: 1rem;
    }
  }
  
  .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    
    .form-group {
      flex: 1;
    }
  }
  
  .form-group {
    margin-bottom: 1.25rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: $text-color;
    }
    
    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid rgba($secondary-color, 0.2);
      border-radius: $border-radius;
      font-size: 0.95rem;
      
      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
      }
      
      &.is-invalid {
        border-color: $error-color;
      }
      
      &:disabled {
        background-color: rgba($secondary-color, 0.05);
        cursor: not-allowed;
      }
    }
    
    textarea.form-control {
      resize: vertical;
    }
    
    .error-feedback {
      color: $error-color;
      font-size: 0.875rem;
      margin-top: 0.5rem;
    }
    
    .form-text {
      color: $light-text;
      font-size: 0.875rem;
      margin-top: 0.5rem;
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
    
    .btn {
      padding: 0.75rem 1.5rem;
      border-radius: $border-radius;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      
      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
      
      &.btn-primary {
        background-color: $primary-color;
        color: white;
        
        &:hover:not(:disabled) {
          background-color: darken($primary-color, 10%);
        }
      }
      
      &.btn-outline {
        background-color: transparent;
        border: 1px solid $secondary-color;
        color: $secondary-color;
        
        &:hover {
          background-color: rgba($secondary-color, 0.1);
        }
      }
    }
  }
}

.participation-section {
  h3 {
    font-size: 1.5rem;
    color: $text-color;
    margin-bottom: 1.5rem;
  }
  
  .empty-state {
    background-color: $light-background;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    padding: 2rem;
    text-align: center;
    
    .empty-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: $light-text;
    }
    
    h4 {
      font-size: 1.25rem;
      color: $text-color;
      margin-bottom: 0.5rem;
    }
    
    p {
      color: $light-text;
      margin-bottom: 1.5rem;
    }
    
    .btn-primary {
      background-color: $primary-color;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: $border-radius;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: darken($primary-color, 10%);
      }
    }
  }
  
  .participation-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
  }
  
  .participation-card {
    background-color: $light-background;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    padding: 1.5rem;
    
    .participation-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;
      
      .project-title {
        font-size: 1.25rem;
        color: $text-color;
        margin: 0;
        flex: 1;
      }
      
      .participation-status {
        padding: 0.25rem 0.5rem;
        border-radius: $border-radius;
        font-size: 0.75rem;
        font-weight: 500;
        
        &.status-registered {
          background-color: rgba($info-color, 0.1);
          color: $info-color;
        }
        
        &.status-confirmed {
          background-color: rgba($primary-color, 0.1);
          color: $primary-color;
        }
        
        &.status-attended {
          background-color: rgba($success-color, 0.1);
          color: $success-color;
        }
        
        &.status-cancelled {
          background-color: rgba($error-color, 0.1);
          color: $error-color;
        }
      }
    }
    
    .participation-details {
      margin-bottom: 1rem;
      
      .detail-item {
        display: flex;
        margin-bottom: 0.5rem;
        
        .detail-label {
          width: 100px;
          color: $light-text;
          font-size: 0.875rem;
        }
        
        .detail-value {
          color: $text-color;
          font-size: 0.875rem;
        }
      }
    }
    
    .participation-feedback {
      margin-bottom: 1rem;
      padding: 0.75rem;
      background-color: rgba($secondary-color, 0.05);
      border-radius: $border-radius;
      
      .feedback-label {
        display: block;
        color: $light-text;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
      }
      
      .feedback-text {
        color: $text-color;
        font-size: 0.875rem;
        margin: 0;
      }
    }
    
    .participation-actions {
      .btn-outline {
        display: inline-block;
        background-color: transparent;
        border: 1px solid $primary-color;
        color: $primary-color;
        padding: 0.5rem 1rem;
        border-radius: $border-radius;
        font-size: 0.875rem;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        
        &:hover {
          background-color: rgba($primary-color, 0.1);
        }
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .profile-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .form-actions {
    flex-direction: column-reverse;
    
    .btn {
      width: 100%;
      margin-bottom: 0.5rem;
    }
  }
  
  .participation-list {
    grid-template-columns: 1fr;
  }
}
