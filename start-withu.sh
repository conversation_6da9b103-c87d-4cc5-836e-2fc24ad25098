#!/bin/bash

echo "==================================================="
echo "             STARTING WITHU APPLICATION"
echo "==================================================="
echo ""

echo "[1/4] Checking prerequisites..."
if ! command -v dotnet &> /dev/null; then
    echo "ERROR: .NET SDK not found. Please install .NET 8 SDK."
    echo "Download from: https://dotnet.microsoft.com/download/dotnet/8.0"
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js not found. Please install Node.js."
    echo "Download from: https://nodejs.org/"
    exit 1
fi

if ! command -v ng &> /dev/null; then
    echo "WARNING: Angular CLI not found. Will attempt to use npx."
    USE_NPX=1
else
    USE_NPX=0
fi

echo "[2/4] Starting .NET API..."
cd WithU.API
dotnet run &
API_PID=$!
cd ..
echo ".NET API starting at http://localhost:5080 and https://localhost:7080"
echo ""

echo "[3/4] Waiting for API to initialize (10 seconds)..."
sleep 10

echo "[4/4] Starting Angular frontend..."
if [ $USE_NPX -eq 1 ]; then
    npx ng serve --open &
else
    ng serve --open &
fi
ANGULAR_PID=$!

echo ""
echo "==================================================="
echo "             WITHU APPLICATION STARTED"
echo "==================================================="
echo ""
echo "API: http://localhost:5080"
echo "Frontend: http://localhost:4200"
echo ""
echo "Your browser should open automatically to http://localhost:4200"
echo ""
echo "Press Ctrl+C to stop all services..."

# Handle graceful shutdown
trap 'echo ""; echo "Stopping services..."; kill $API_PID $ANGULAR_PID 2>/dev/null; echo "Services stopped."; echo ""; echo "Thank you for using WithU!"; sleep 3; exit 0' INT

# Wait for user to press Ctrl+C
wait
