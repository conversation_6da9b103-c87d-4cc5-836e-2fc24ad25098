using Microsoft.AspNetCore.Mvc;
using WithU.API.Data;
using WithU.API.Models;

namespace WithU.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DebugController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DebugController> _logger;

        public DebugController(ApplicationDbContext context, ILogger<DebugController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet("users")]
        public IActionResult GetUsers()
        {
            var users = _context.Users.ToList();
            return Ok(new { users });
        }

        [HttpGet("projects")]
        public IActionResult GetProjects()
        {
            var projects = _context.Projects.ToList();
            return Ok(new { projects });
        }

        [HttpGet("participations")]
        public IActionResult GetParticipations()
        {
            var participations = _context.ProjectParticipations.ToList();
            return Ok(new { participations });
        }
    }
}
