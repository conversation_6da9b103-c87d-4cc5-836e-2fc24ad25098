using Microsoft.EntityFrameworkCore;
using WithU.API.Data;
using WithU.API.Models;

namespace WithU.API.Repositories
{
    public class EfProjectRepository : IProjectRepository
    {
        private readonly ApplicationDbContext _context;

        public EfProjectRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Project>> GetAllAsync()
        {
            return await _context.Projects.ToListAsync();
        }

        public async Task<Project?> GetByIdAsync(int id)
        {
            return await _context.Projects.FindAsync(id);
        }

        public async Task<Project> CreateAsync(Project project)
        {
            project.CreatedAt = DateTime.UtcNow;
            project.UpdatedAt = DateTime.UtcNow;
            
            _context.Projects.Add(project);
            await _context.SaveChangesAsync();
            
            return project;
        }

        public async Task<Project?> UpdateAsync(int id, Project updatedProject)
        {
            var existingProject = await _context.Projects.FindAsync(id);
            if (existingProject == null)
            {
                return null;
            }

            // Update properties
            existingProject.Title = updatedProject.Title;
            existingProject.Description = updatedProject.Description;
            existingProject.Image = updatedProject.Image;
            existingProject.Location = updatedProject.Location;
            existingProject.Date = updatedProject.Date;
            existingProject.Status = updatedProject.Status;
            existingProject.Category = updatedProject.Category;
            existingProject.CompanyName = updatedProject.CompanyName;
            existingProject.SkillsRequired = updatedProject.SkillsRequired;
            existingProject.MaxParticipants = updatedProject.MaxParticipants;
            existingProject.ContactEmail = updatedProject.ContactEmail;
            existingProject.ContactPhone = updatedProject.ContactPhone;
            existingProject.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return existingProject;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var project = await _context.Projects.FindAsync(id);
            if (project == null)
            {
                return false;
            }

            _context.Projects.Remove(project);
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
