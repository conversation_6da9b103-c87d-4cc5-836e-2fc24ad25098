using System.ComponentModel.DataAnnotations;

namespace WithU.API.Models
{
    public class User
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50, MinimumLength = 2)]
        public string FirstName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50, MinimumLength = 2)]
        public string LastName { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; } = string.Empty; // In a real app, this would be hashed
        
        public string? PhoneNumber { get; set; }
        
        public string? ProfileImage { get; set; }
        
        public string? Bio { get; set; }
        
        public string? Skills { get; set; } // Comma-separated list of skills
        
        public string? CompanyName { get; set; }
        
        public string? JobTitle { get; set; }
        
        public string Role { get; set; } = "Employee"; // Employee, CompanyAdmin, SystemAdmin
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation property (would be used with EF Core)
        // public ICollection<ProjectParticipation> Participations { get; set; } = new List<ProjectParticipation>();
        
        // Helper property to get full name
        public string FullName => $"{FirstName} {LastName}";
    }
}
